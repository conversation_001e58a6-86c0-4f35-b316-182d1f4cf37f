<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <application
        android:name="com.ygxj.mcs.action.AppApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:screenOrientation="portrait"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- 表示当前已经适配了分区存储 -->
        <meta-data
            android:name="ScopedStorage"
            android:value="true" />

        <!-- 屏幕适配 -->
        <meta-data
            android:name="design_width_in_dp"
            android:value="640" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="360" />

        <!-- 闪屏页 -->
        <activity
            android:name="com.ygxj.mcs.ui.splash.SplashActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/SplashTheme"
            tools:ignore="LockedOrientationActivity">
            <!-- 程序入口 -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 登录页 -->
        <activity
            android:name="com.ygxj.mcs.ui.login.LoginActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape"
            android:windowSoftInputMode="adjustPan" />

        <!-- 注册页 -->
        <activity
            android:name="com.ygxj.mcs.ui.login.RegisterActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape"
            android:windowSoftInputMode="adjustPan" />

        <!-- 主页面 -->
        <activity
            android:name="com.ygxj.mcs.ui.main.MainActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 主页面 -->
        <activity
            android:name="com.ygxj.mcs.ui.main.MainPageActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 量表详情 -->
        <activity
            android:name="com.ygxj.mcs.ui.evaluate.ScaleDetailActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 量表详情 -->
        <activity
            android:name="com.ygxj.mcs.ui.evaluate.ScaleTrainActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 量表评估 -->
        <activity
            android:name="com.ygxj.mcs.ui.evaluate.ScaleActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 压力评估 -->
        <activity
            android:name="com.ygxj.mcs.ui.evaluate.PressureActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 资源列表 -->
        <activity
            android:name="com.ygxj.mcs.ui.relax.ResourceListActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 音乐播放界面 -->
        <activity
            android:name="com.ygxj.mcs.ui.relax.MusicPlayActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 个人数据详情 -->
        <activity
            android:name="com.ygxj.mcs.ui.data.PersonDataDetailActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 团队数据详情 -->
        <activity
            android:name="com.ygxj.mcs.ui.data.TeamDataDetailActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 体心训练 -->
        <activity
            android:name="com.ygxj.mcs.ui.train.TiXinTrainListActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 系统推荐训练 -->
        <activity
            android:name="com.ygxj.mcs.ui.train.SystemTrainListActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 身心训练 -->
        <activity
            android:name="com.ygxj.mcs.ui.train.ShenXinTrainListActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 团体训练房间 -->
        <activity
            android:name="com.ygxj.mcs.ui.train.TeamRoomActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 团体训练 -->
        <activity
            android:name="com.ygxj.mcs.ui.train.TeamTrainActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 训练界面 -->
        <activity
            android:name="com.ygxj.mcs.ui.train.TrainActivity"
            android:exported="false"
            android:keepScreenOn="true"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <!-- 蓝牙设备管理 -->
        <activity
            android:name="com.ygxj.mcs.ui.train.DeviceManagerActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

    </application>

</manifest>