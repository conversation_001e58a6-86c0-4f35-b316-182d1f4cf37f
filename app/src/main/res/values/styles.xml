<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- 应用主题样式 -->
    <style name="AppTheme" parent="QMUI.Compat.NoActionBar">
        <!-- 窗口背景颜色 -->
        <item name="android:windowBackground">@color/white</item>
        <!-- 应用的主要色调，ActionBar Toolbar 默认使用该颜色 -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <!-- 应用的主要暗色调，状态栏默认使用该颜色 -->
        <item name="colorPrimaryDark">@color/common_primary_dark_color</item>
        <!-- 应用的强调色，CheckBox RadioButton Switch 等一般控件的选中效果默认采用该颜色 -->
        <item name="colorAccent">@color/common_accent_color</item>
        <!-- 默认文本颜色，Button、TextView 的文字颜色 -->
        <item name="android:textColor">@color/common_text_color3</item>
        <!-- 默认字体大小，Button、TextView 的字体大小 -->
        <item name="android:textSize">14sp</item>
        <!-- 默认提示颜色，Button、TextView 的提示文字颜色 -->
        <item name="android:textColorHint">@color/common_text_hint_color</item>
        <!-- ActionMode 覆盖 Actionbar 不被顶下来 -->
        <item name="windowActionModeOverlay">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- ActionMode 的颜色 -->
        <item name="actionModeBackground">@color/colorPrimary</item>
        <!-- 解决 Android 5.1 及以上版本 Button 英文字符串自动变大写的问题 -->
        <item name="android:textAllCaps">false</item>
        <!-- 解决 ImageView 不能自动等比拉伸图片的问题 -->
        <item name="android:adjustViewBounds">true</item>
        <!-- 默认隐藏输入法，开启这个选项后会导致输入对话框在关闭之后不能关闭软键盘 -->
        <!--<item name="android:windowSoftInputMode">stateHidden</item>-->
        <!-- 关闭 RecyclerView NestedScrollView ViewPager 水波纹效果 -->
        <item name="android:overScrollMode">never</item>
        <!-- 关闭 TextView 自带的文字间距 -->
        <item name="android:includeFontPadding">false</item>

    </style>

    <!-- 全屏主题样式 -->
    <style name="FullScreenTheme" parent="AppTheme">
        <item name="android:windowFullscreen">true</item>
    </style>

    <!-- 闪屏页主题样式 -->
    <style name="SplashTheme" parent="FullScreenTheme">
        <!-- https://www.jianshu.com/p/d0d907754603 -->
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <style name="ContentLoadingProgress">
        <item name="colorAccent">@color/colorPrimary</item>
    </style>

    <style name="UserInfoLabel">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/textColor</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="BtnRelax">
        <item name="android:drawablePadding">10dp</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textSize">12sp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="progress_bar_style">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/progress_bar</item>
        <item name="android:indeterminateDrawable">
            @android:drawable/progress_indeterminate_horizontal
        </item>
        <item name="android:minHeight">8dp</item>
        <item name="android:maxHeight">8dp</item>
        <item name="android:mirrorForRtl">true</item>
    </style>

    <style name="TrainDataListHeaderLabel">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">8sp</item>
        <item name="android:textColor">@color/colorPrimary</item>
    </style>

    <style name="TrainDataListItemLabel" parent="TrainDataListHeaderLabel">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">10sp</item>
    </style>

    <style name="TrainDataDetailCircleLabel">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">8sp</item>
        <item name="android:textColor">@color/lightestBlack</item>
    </style>

    <style name="TrainDataDetailCircleData">
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:layout_marginBottom">4dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="ChartLabel">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/shape_with_border_and_more_radius</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingTop">5dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:paddingBottom">5dp</item>
        <item name="android:layout_marginBottom">30dp</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textSize">10sp</item>
    </style>

    <style name="btnClear">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginTop">5dp</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:background">@drawable/bg_btn_start_train</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:paddingStart">6dp</item>
        <item name="android:paddingEnd">6dp</item>
        <item name="android:paddingTop">2dp</item>
        <item name="android:paddingBottom">2dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">9sp</item>
    </style>
    <style name="deviceContainer">
        <item name="android:layout_width">400dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/shape_with_border_bg</item>
        <item name="android:padding">15dp</item>
        <item name="android:layout_marginBottom">5dp</item>
    </style>

    <style name="deviceType">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="deviceName">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">10sp</item>
    </style>

    <style name="deviceState">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">10sp</item>
    </style>

    <style name="deviceProgress" parent="@style/Base.Widget.AppCompat.ProgressBar">
        <item name="android:visibility">invisible</item>
        <item name="android:layout_width">10dp</item>
        <item name="android:layout_height">10dp</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginStart">5dp</item>
        <item name="android:max">100</item>
        <item name="android:progress">50</item>
    </style>
    <style name="btnCommon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginTop">5dp</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:background">@drawable/bg_btn_start_train</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingTop">6dp</item>
        <item name="android:paddingEnd">20dp</item>
        <item name="android:paddingBottom">6dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
    </style>
</resources>