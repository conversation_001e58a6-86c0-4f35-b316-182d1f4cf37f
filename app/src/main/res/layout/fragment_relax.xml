<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal"
        tools:background="@drawable/bg_main"
        tools:context="com.ygxj.mcs.ui.main.RelaxFragment"
        tools:ignore="HardcodedText">

        <com.ygxj.mcs.other.PressAlphaTextView
            android:id="@+id/tvMusicRelax"
            style="@style/BtnRelax"
            android:text="音乐放松体验"
            app:drawableTopCompat="@drawable/btn_relax_music" />

        <com.ygxj.mcs.other.PressAlphaTextView
            android:id="@+id/tvImgRelax"
            style="@style/BtnRelax"
            android:layout_marginStart="40dp"
            android:text="图片放松体验"
            app:drawableTopCompat="@drawable/btn_relax_img" />

        <com.ygxj.mcs.other.PressAlphaTextView
            android:id="@+id/tvVrRelax"
            android:layout_marginStart="40dp"
            style="@style/BtnRelax"
            android:text="深度放松体验"
            android:visibility="visible"
            app:drawableTopCompat="@drawable/btn_relax_vr" />
    </LinearLayout>

</layout>