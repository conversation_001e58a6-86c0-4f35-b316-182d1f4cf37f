<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@drawable/bg_main"
        tools:context="com.ygxj.mcs.ui.train.ShenXinTrainListActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tvScaleTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_train_type_title"
            android:gravity="center"
            android:text="身心训练"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <FrameLayout
            android:id="@+id/fragmentContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:background="@drawable/bg_main"/>

    </LinearLayout>

</layout>