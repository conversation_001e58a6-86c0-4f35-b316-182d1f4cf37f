<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>


    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@drawable/bg_main"
        tools:context="com.ygxj.mcs.ui.train.SelfTrainFragment">


        <LinearLayout
            android:id="@+id/llMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/btnTrainStart"
            android:layout_marginStart="100dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="100dp"
            android:background="@drawable/shape_with_border_and_radius"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="训练时长:"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvTrainDuration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/shape_with_border_and_radius"
                    android:hint="请选择时长"
                    android:padding="6dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/textColor"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="训练场景:"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvTrainScene"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/shape_with_border_and_radius"
                    android:hint="请选择训练场景"
                    android:padding="6dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/textColor"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="训练指标:"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <SeekBar
                    android:id="@+id/seekBar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="80" />

                <TextView
                    android:id="@+id/tvProgress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="80%"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>

        </LinearLayout>

        <Button
            android:id="@+id/btnTrainStart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/bg_btn_start_train"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:paddingStart="10dp"
            android:paddingTop="4dp"
            android:paddingEnd="10dp"
            android:paddingBottom="4dp"
            android:text="开始训练"
            android:textColor="@color/white"
            android:textSize="12sp" />

    </RelativeLayout>
</layout>