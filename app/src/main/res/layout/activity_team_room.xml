<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.train.TeamRoomActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.drake.statelayout.StateLayout
                android:id="@+id/state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/llBottom"
                android:layout_alignParentTop="true">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    app:spanCount="5"
                    tools:itemCount="10"
                    tools:listitem="@layout/item_team_room_user"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </com.drake.statelayout.StateLayout>

            <LinearLayout
                android:id="@+id/llBottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:gravity="center"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnReady"
                    style="@style/btnCommon"
                    android:layout_marginEnd="10dp"
                    android:text="准备" />

                <Button
                    android:id="@+id/btnDevice"
                    style="@style/btnCommon"
                    android:layout_marginStart="10dp"
                    android:text="设备管理" />
            </LinearLayout>
            <!--倒计时-->
            <TextView
                android:id="@+id/tvReadyTimer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text=""
                android:textColor="@color/white"
                android:textSize="70sp"
                android:textStyle="bold" />
        </RelativeLayout>
    </LinearLayout>

</layout>