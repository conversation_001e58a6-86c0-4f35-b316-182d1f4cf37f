<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.ygxj.mcs.ui.train.TrainViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.train.TrainActivity">

        <androidx.media3.ui.PlayerView
            android:id="@+id/player_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:resize_mode="fill"
            app:use_controller="false" />

        <ImageView
            android:id="@+id/ivBgDefault"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            android:scaleType="centerCrop"
            android:src="@drawable/bg_train_default" />

        <!--倒计时-->
        <TextView
            android:id="@+id/tvReadyTimer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@{model.readyTimerText}"
            android:textColor="@color/white"
            android:textSize="70sp"
            android:textStyle="bold"
            android:visibility="@{model.showReadyTimerText?View.VISIBLE:View.GONE}"
            tools:text="5"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/btnStartTrainBig"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/bg_btn_start_train_big"
            android:gravity="center"
            android:text="开始训练"
            android:textColor="@color/white"
            android:textSize="25sp" />
        <!--top-->
        <LinearLayout
            android:id="@+id/llTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_train_top"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@null"
                android:padding="15dp"
                android:src="@drawable/ic_return" />

            <TextView
                android:id="@+id/tvUserName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text='@{"测试者："+model.userName}'
                android:textColor="@color/white"
                android:textSize="13sp"
                tools:text="测试者：--" />

            <TextView
                android:id="@+id/tvCurrentScene"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text='@{"当前训练场景："+model.sceneName}'
                android:textColor="@color/white"
                android:textSize="13sp"
                tools:text="当前训练场景：江上大桥" />

            <Button
                android:id="@+id/btnTrainIntro"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_btn_start_train"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:paddingStart="10dp"
                android:paddingTop="4dp"
                android:paddingEnd="10dp"
                android:paddingBottom="4dp"
                android:text="训练说明"
                android:textColor="@color/white"
                android:textSize="13sp" />
        </LinearLayout>
        <!--center-->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/llTitle"
            android:layout_alignParentEnd="true"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="210dp"
                android:layout_height="150dp"
                android:background="#4D000000"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/llTrainChoose1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingStart="20dp"
                    android:paddingTop="10dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="10dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="实时心率值："
                            android:textColor="@color/white"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/tvHeartRate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:minWidth="30dp"
                            android:text="@{model.heartRate}"
                            android:textColor="@color/white"
                            android:textSize="13sp"
                            tools:text="--" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="实时血氧值："
                            android:textColor="@color/white"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/tvBloodOxygen"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:minWidth="30dp"
                            android:text="@{model.bloodOxygen}"
                            android:textColor="@color/white"
                            android:textSize="13sp"
                            tools:text="--" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="实时耗氧量："
                            android:textColor="@color/white"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/tvOxygenConsumption"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:minWidth="30dp"
                            android:text="@{model.oxygenConsumption}"
                            android:textColor="@color/white"
                            android:textSize="13sp"
                            tools:text="--" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="实时呼吸值："
                            android:textColor="@color/white"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/tvBreathValue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:minWidth="30dp"
                            android:text="@{model.breathValue}"
                            android:textColor="@color/white"
                            android:textSize="13sp"
                            tools:text="--" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llTrainChoose2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvTrainHeart"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:gravity="center"
                        android:text="心率："
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/lineChartHeart"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llTrainChoose3"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvTrainBreath"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:gravity="center"
                        android:text="呼吸："
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/lineChartBreath"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llChatNumber"
                android:layout_width="210dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvTrainChoose1"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:background="@drawable/bg_train_choose_index"
                    android:gravity="center"
                    android:text="1"
                    android:textColor="@color/colorOpacity"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvTrainChoose2"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@drawable/bg_train_choose_index"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="@color/colorOpacity"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvTrainChoose3"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:background="@drawable/bg_train_choose_index"
                    android:gravity="center"
                    android:text="3"
                    android:textColor="@color/colorOpacity"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <!--bottom-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_train_bottom"
            android:gravity="center_vertical"
            android:orientation="horizontal">


            <Button
                android:id="@+id/btnStartTrain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/bg_btn_start_train"
                android:enabled="@{model.toggleBtnStartEnable}"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:paddingStart="8dp"
                android:paddingTop="4dp"
                android:paddingEnd="8dp"
                android:paddingBottom="4dp"
                android:text="开始训练"
                android:textColor="@color/white"
                android:textSize="13sp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvTrainDuration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{model.trainDuration}"
                    android:textColor="@color/white"
                    android:textSize="13sp"
                    tools:text="15:00" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="剩余时长"
                    android:textColor="@color/colorPrimary"
                    android:textSize="13sp" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvSpeed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{model.speed}"
                        android:textColor="@color/white"
                        android:textSize="13sp"
                        tools:text="0" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" KM/H"
                        android:textColor="@color/white"
                        android:textSize="13sp" />

                </LinearLayout>


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="运动速度"
                    android:textColor="@color/colorPrimary"
                    android:textSize="13sp" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDistance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{model.distance}"
                        android:textColor="@color/white"
                        android:textSize="13sp"
                        tools:text="0.00" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" KM"
                        android:textColor="@color/white"
                        android:textSize="13sp" />

                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="运动距离"
                    android:textColor="@color/colorPrimary"
                    android:textSize="13sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvCalories"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{model.calories}"
                        android:textColor="@color/white"
                        android:textSize="13sp"
                        tools:text="0" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" CAL"
                        android:textColor="@color/white"
                        android:textSize="13sp" />
                </LinearLayout>


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="消耗卡路里"
                    android:textColor="@color/colorPrimary"
                    android:textSize="13sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvTrainState"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{model.trainState}"
                        android:textColor="@color/white"
                        android:textSize="13sp"
                        tools:text="未开始" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="训练状态"
                    android:textColor="@color/colorPrimary"
                    android:textSize="13sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_smart"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvSmartAdjustment"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text='@{model.smartAdjustment? @string/smart_on:@string/smart_off}'
                        android:textColor="@color/white"
                        android:textSize="13sp"
                        tools:text="关闭" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="智能调整"
                    android:textColor="@color/colorPrimary"
                    android:textSize="13sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <Button
                    android:id="@+id/btnDeviceManage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_btn_start_train"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:paddingStart="8dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="8dp"
                    android:paddingBottom="4dp"
                    android:text="设备管理"
                    android:textColor="@color/white"
                    android:textSize="13sp" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>

</layout>