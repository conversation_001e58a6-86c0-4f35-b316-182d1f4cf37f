<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.train.TiXinTrainListActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_train_type_title"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvNormalTrain"
                android:layout_width="120dp"
                android:layout_height="35dp"
                android:background="@drawable/selector_train_button"
                android:gravity="center"
                android:text="基础训练"
                android:textColor="@color/selector_train_item"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tvStrengthTrain"
                android:layout_width="120dp"
                android:layout_height="35dp"
                android:background="@drawable/selector_train_button"
                android:gravity="center"
                android:text="加强训练"
                android:textColor="@color/selector_train_item"
                android:textSize="13sp" />

        </LinearLayout>

        <com.drake.statelayout.StateLayout
            android:id="@+id/state"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/item"
                    android:layout_width="100dp"
                    android:layout_height="75dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/bg_train_list_item1"
                    android:gravity="center"
                    tools:ignore="UselessParent">

                    <TextView
                        android:id="@+id/tvTrainName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        tools:text="训练名称" />
                </LinearLayout>

                <ScrollView
                    android:id="@+id/slTrainIntro"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="100dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="100dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_with_border_and_radius"
                    android:paddingStart="6dp"
                    android:paddingTop="6dp"
                    android:paddingEnd="6dp"
                    android:scrollbars="none"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvTrainIntro"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:lineSpacingExtra="2dp"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        tools:text="训练说明" />

                </ScrollView>

                <Button
                    android:id="@+id/btnTrainStart"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/bg_btn_start_train"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:paddingStart="10dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="10dp"
                    android:paddingBottom="4dp"
                    android:text="开始训练"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>
        </com.drake.statelayout.StateLayout>

    </LinearLayout>

</layout>