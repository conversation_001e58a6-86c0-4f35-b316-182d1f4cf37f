<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.train.TeamTrainActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_train_type_title"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvInvite"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="邀请训练"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvTeamMission"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:alpha="0.5"
                android:gravity="center"
                android:text="团体训练任务"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>

        <com.qmuiteam.qmui.widget.QMUIViewPager
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

</layout>