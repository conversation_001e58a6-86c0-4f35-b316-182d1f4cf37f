<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.mcs.http.model.TrainResultData" />
    </data>

    <LinearLayout
        android:id="@+id/item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:background="@drawable/bg_train_data_item"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        tools:ignore="HardcodedText">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="40dp"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvResult"
                style="@style/TrainDataListItemLabel"
                android:text="@{m.result}"
                android:textSize="18sp"
                tools:text="成功" />

            <TextView
                style="@style/TrainDataListItemLabel"
                android:text="训练结果"
                android:textSize="8sp" />

        </LinearLayout>

        <View
            android:layout_width="18dp"
            android:layout_height="match_parent"
            android:background="@drawable/bg_train_data_item_line" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                style="@style/TrainDataListHeaderLabel"
                android:text="开始时间" />

            <TextView
                android:id="@+id/tvStartTime"
                style="@style/TrainDataListItemLabel"
                android:layout_marginBottom="4dp"
                android:text="@{m.startTime}"
                tools:text="2019年12月12日14:02:38" />

            <TextView
                style="@style/TrainDataListHeaderLabel"
                android:text="结束时间" />

            <TextView
                android:id="@+id/tvEndTime"
                style="@style/TrainDataListItemLabel"
                android:text="@{m.endTime}"
                tools:text="2019年12月12日14:02:41" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                style="@style/TrainDataListHeaderLabel"
                android:text="训练类型" />

            <TextView
                android:id="@+id/tvType"
                style="@style/TrainDataListItemLabel"
                android:layout_marginBottom="4dp"
                android:text="@{m.trainType}"
                tools:text="基础" />

            <TextView
                style="@style/TrainDataListHeaderLabel"
                android:text="训练场景" />

            <TextView
                android:id="@+id/tvScene"
                style="@style/TrainDataListItemLabel"
                android:text="@{m.trainScene}"
                tools:text="鲜花大道你好" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDistanceOrTeamNameLabel"
                style="@style/TrainDataListHeaderLabel"
                android:text="房间名称" />

            <TextView
                android:id="@+id/tvDistanceOrTeamName"
                style="@style/TrainDataListItemLabel"
                android:layout_marginBottom="4dp"
                android:text="@{m.roomName}"
                tools:text="邀请训练" />

            <TextView
                android:id="@+id/tvSpeedOrCreatorLabel"
                style="@style/TrainDataListHeaderLabel"
                android:text="当前用户" />

            <TextView
                android:id="@+id/tvSpeedOrCreator"
                style="@style/TrainDataListItemLabel"
                android:text="@{m.userName}"
                tools:text="admin" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvCaloriesOrTeamCountLabel"
                style="@style/TrainDataListHeaderLabel"
                android:text="组队人数" />

            <TextView
                android:id="@+id/tvCaloriesOrTeamCount"
                style="@style/TrainDataListItemLabel"
                android:text='@{m.usersCount+"人"}'
                tools:text="2人" />
        </LinearLayout>


    </LinearLayout>
</layout>