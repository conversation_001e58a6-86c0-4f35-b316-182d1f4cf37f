<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.evaluate.ScaleDetailActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.drake.statelayout.StateLayout
            android:id="@+id/state"
            android:layout_width="412dp"
            android:layout_height="229dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_scale_detail"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvScaleTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:padding="5dp"
                    android:text="心理评估1"
                    android:textColor="@color/colorPrimary"
                    android:textSize="12sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@drawable/scale_detail_line" />

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/slMainView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="30dp"
                    android:layout_marginEnd="30dp"
                    android:layout_weight="1"
                    android:scrollbars="none"
                    tools:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="10dp">

                        <TextView
                            android:id="@+id/tvQuestionTitle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/scaleTextColor"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="1. 你对本次测试了解吗?" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="4"
                            tools:listitem="@layout/item_option" />
                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@drawable/scale_detail_line" />

                <RelativeLayout
                    android:id="@+id/rlBtnView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="5dp"
                    tools:visibility="visible">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btnPre"
                            android:layout_width="65dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/btn_scale"
                            android:minWidth="0dp"
                            android:minHeight="0dp"
                            android:text="上一题"
                            android:textColor="@color/white"
                            android:textSize="12sp" />

                        <Button
                            android:id="@+id/btnNext"
                            android:layout_width="65dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/btn_scale"
                            android:minWidth="0dp"
                            android:minHeight="0dp"
                            android:text="下一题"
                            android:textColor="@color/white"
                            android:textSize="12sp" />

                        <Button
                            android:id="@+id/btnCommit"
                            android:layout_width="65dp"
                            android:layout_height="24dp"
                            android:background="@drawable/btn_scale_commit"
                            android:minWidth="0dp"
                            android:minHeight="0dp"
                            android:text="完成测试"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:visibility="gone"
                            tools:visibility="visible" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvQuestionIndex"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:padding="5dp"
                        android:text="1/25"
                        android:textColor="@color/colorPrimary"
                        android:textSize="10sp" />
                </RelativeLayout>

            </LinearLayout>
        </com.drake.statelayout.StateLayout>


    </RelativeLayout>

</layout>