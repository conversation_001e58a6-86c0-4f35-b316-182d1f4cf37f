<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.mcs.http.model.ResourceData" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="10dp"
        tools:background="@color/transparent"
        tools:ignore="SmallSp">

        <RelativeLayout
            android:id="@+id/item"
            android:layout_width="90dp"
            android:layout_height="120dp"
            android:background="@drawable/shape_with_bg"
            android:gravity="center_horizontal"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            tools:ignore="UselessParent">

            <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                android:id="@+id/ivImage"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:scaleType="centerCrop"
                app:qmui_corner_radius="4dp"
                tools:src="@drawable/music_img_1" />

            <TextView
                android:id="@+id/tvImageTitle"
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:layout_below="@id/ivImage"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:lineSpacingExtra="2dp"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:text="音乐的名称音乐的名称音乐的名称音乐的名称音乐的名称" />
        </RelativeLayout>
    </RelativeLayout>
</layout>