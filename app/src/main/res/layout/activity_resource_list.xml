<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context=".page.module.relax.ResourceListActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_train_type_title"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvResourceType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:text="音乐放松体验" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="40dp"
            android:layout_marginEnd="40dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/shape_with_border_and_radius"
            android:orientation="vertical">

            <com.drake.statelayout.StateLayout
                android:id="@+id/state"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingTop="15dp"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="5"
                    tools:itemCount="10"
                    tools:listitem="@layout/item_music_list" />
            </com.drake.statelayout.StateLayout>

            <LinearLayout
                android:id="@+id/llVrIpConfigView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:gravity="end|center_vertical"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvVrIp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/textColor"
                    android:textSize="12sp"
                    android:text="当前VR设备地址：未配置" />

                <TextView
                    android:id="@+id/tvConfig"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:text="修改配置"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</layout>