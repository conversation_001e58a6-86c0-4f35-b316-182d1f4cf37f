<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.mcs.http.model.TrainListData" />
    </data>

    <LinearLayout
        android:id="@+id/llContainer"
        selected="@{m.checked}"
        android:layout_width="100dp"
        android:layout_height="75dp"
        android:background="@drawable/selector_train_list_item"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp">

        <LinearLayout
            android:id="@+id/item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="2dp"
            android:background="@drawable/bg_train_list_item1"
            android:gravity="center"
            tools:ignore="UselessParent">

            <TextView
                android:id="@+id/tvTrainName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{m.TrainName}"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:text="训练名称" />
        </LinearLayout>
    </LinearLayout>
</layout>


