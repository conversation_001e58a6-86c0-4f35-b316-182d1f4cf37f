<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data />

    <com.hjq.shape.layout.ShapeLinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:shape_radius="2dp"
        app:shape_solidColor="@color/white">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="20dp"
            android:text="您是否为运动神经训练新手？"
            android:textSize="12sp" />

        <RadioGroup
            android:id="@+id/rg_new"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:buttonTint="#1b88ee"
                android:text="新手"
                android:textColor="@color/common_text_color3"
                android:textSize="12sp" />

            <RadioButton
                android:id="@+id/rb_old"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:buttonTint="#1b88ee"
                android:text="有训练经验"
                android:textColor="@color/common_text_color3"
                android:textSize="12sp" />

        </RadioGroup>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="20dp"
            android:text="您是否有运动习惯？"
            android:textSize="12sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:buttonTint="#1b88ee"
                android:text="有固定运动习惯"
                android:textColor="@color/common_text_color3"
                android:textSize="12sp" />

            <RadioButton
                android:id="@+id/rb2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:buttonTint="#1b88ee"
                android:text="偶尔运动"
                android:textColor="@color/common_text_color3"
                android:textSize="12sp" />
        </LinearLayout>

        <RadioButton
            android:id="@+id/rb3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:buttonTint="#1b88ee"
            android:text="几乎不运动"
            android:textColor="@color/common_text_color3"
            android:textSize="12sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="取消"
                android:textColor="#1b88ee"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:text="确定"
                android:textColor="#1b88ee"
                android:textSize="12sp" />
        </LinearLayout>
    </com.hjq.shape.layout.ShapeLinearLayout>
</layout>