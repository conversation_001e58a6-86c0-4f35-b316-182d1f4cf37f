<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.train.TiXinTrainListActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_train_type_title"
            android:orientation="horizontal">

            <Space
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5" />

            <TextView
                android:id="@+id/tvSelfTrain"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="5dp"
                android:layout_marginTop="2dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="2dp"
                android:layout_weight="1"
                android:background="@drawable/selector_train_button"
                android:gravity="center"
                android:text="自主训练"
                android:textColor="@color/selector_train_item"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tvNormalTrain"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="5dp"
                android:layout_marginTop="2dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="2dp"
                android:layout_weight="1"
                android:background="@drawable/selector_train_button"
                android:gravity="center"
                android:text="基础训练"
                android:textColor="@color/selector_train_item"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tvStrengthTrain"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="5dp"
                android:layout_marginTop="2dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="2dp"
                android:layout_weight="1"
                android:background="@drawable/selector_train_button"
                android:gravity="center"
                android:text="加强训练"
                android:textColor="@color/selector_train_item"
                android:textSize="13sp" />

            <Space
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5" />
        </LinearLayout>

        <com.qmuiteam.qmui.widget.QMUIViewPager
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

</layout>