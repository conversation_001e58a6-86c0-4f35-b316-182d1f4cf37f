<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.data.TeamDataDetailActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tvDataUserName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_with_bg"
            android:gravity="center"
            android:padding="8dp"
            android:textColor="@color/white"
            android:textSize="10sp"
            tools:text="admin的数据" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:baselineAligned="false"
            android:orientation="horizontal">

            <ScrollView
                android:id="@+id/slMainView"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:scrollbars="none"
                tools:ignore="SmallSp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    tools:ignore="HardcodedText">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="20dp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="20dp">

                            <com.mikhaellopez.circularprogressbar.CircularProgressBar
                                android:id="@+id/mcsHeartRate"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                app:cpb_background_progressbar_color="#6690b7"
                                app:cpb_background_progressbar_width="3dp"
                                app:cpb_progress="0"
                                app:cpb_progress_direction="to_left"
                                app:cpb_progress_max="1"
                                app:cpb_progressbar_color_end="#D12C2F"
                                app:cpb_progressbar_color_start="#FACCCF"
                                app:cpb_progressbar_width="3dp"
                                app:cpb_round_border="false" />

                            <LinearLayout
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="平均心率" />

                                <TextView
                                    android:id="@+id/tvHeartRate"
                                    style="@style/TrainDataDetailCircleData"
                                    android:text="--" />

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="BPM" />
                            </LinearLayout>

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="20dp">

                            <com.mikhaellopez.circularprogressbar.CircularProgressBar
                                android:id="@+id/mcsSpeed"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                app:cpb_background_progressbar_color="#6690b7"
                                app:cpb_background_progressbar_width="3dp"
                                app:cpb_progress="0"
                                app:cpb_progress_direction="to_left"
                                app:cpb_progress_max="1"
                                app:cpb_progressbar_color_end="#065797"
                                app:cpb_progressbar_color_start="#2cfefb"
                                app:cpb_progressbar_width="3dp"
                                app:cpb_round_border="false" />

                            <LinearLayout
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="平均速度" />

                                <TextView
                                    android:id="@+id/tvSpeed"
                                    style="@style/TrainDataDetailCircleData"
                                    android:text="--" />

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="M/MIN" />
                            </LinearLayout>


                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="20dp">

                            <com.mikhaellopez.circularprogressbar.CircularProgressBar
                                android:id="@+id/mcsOxygenConsumption"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                app:cpb_background_progressbar_color="#6690b7"
                                app:cpb_background_progressbar_width="3dp"
                                app:cpb_progress="0"
                                app:cpb_progress_direction="to_left"
                                app:cpb_progress_max="1"
                                app:cpb_progressbar_color_end="#6D2ECD"
                                app:cpb_progressbar_color_start="#99B499"
                                app:cpb_progressbar_width="3dp"
                                app:cpb_round_border="false" />

                            <LinearLayout
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="平均耗氧量" />

                                <TextView
                                    android:id="@+id/tvOxygenConsumption"
                                    style="@style/TrainDataDetailCircleData"
                                    android:text="--" />

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="ML/MIN" />
                            </LinearLayout>


                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="20dp">

                            <com.mikhaellopez.circularprogressbar.CircularProgressBar
                                android:id="@+id/mcsCalories"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                app:cpb_background_progressbar_color="#6690b7"
                                app:cpb_background_progressbar_width="3dp"
                                app:cpb_progress="0"
                                app:cpb_progress_direction="to_left"
                                app:cpb_progress_max="1"
                                app:cpb_progressbar_color_end="#FD5713"
                                app:cpb_progressbar_color_start="#CCFF75"
                                app:cpb_progressbar_width="3dp"
                                app:cpb_round_border="false" />

                            <LinearLayout
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="消耗卡路里" />

                                <TextView
                                    android:id="@+id/tvCalories"
                                    style="@style/TrainDataDetailCircleData"
                                    android:text="--" />

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="CAL" />
                            </LinearLayout>


                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="20dp">

                            <com.mikhaellopez.circularprogressbar.CircularProgressBar
                                android:id="@+id/mcsBreath"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                app:cpb_background_progressbar_color="#6690b7"
                                app:cpb_background_progressbar_width="3dp"
                                app:cpb_progress="0"
                                app:cpb_progress_direction="to_left"
                                app:cpb_progress_max="1"
                                app:cpb_progressbar_color_end="#FF5722"
                                app:cpb_progressbar_color_start="#F5B29D"
                                app:cpb_progressbar_width="3dp"
                                app:cpb_round_border="false" />

                            <LinearLayout
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="平均呼吸值" />

                                <TextView
                                    android:id="@+id/tvBreath"
                                    style="@style/TrainDataDetailCircleData"
                                    android:text="--" />

                                <TextView
                                    style="@style/TrainDataDetailCircleLabel"
                                    android:text="MG(ΜL)/(H·G)"
                                    android:textSize="8sp" />
                            </LinearLayout>


                        </RelativeLayout>

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvAdvice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:lineSpacingMultiplier="1.2"
                        android:paddingStart="15dp"
                        android:paddingEnd="15dp"
                        android:text="建议：多喝水，多运动，管住嘴，迈开腿。多喝水，多运动，管住嘴，迈开腿。多喝水，多运动，管住嘴，迈开腿。多喝水，多运动，管住嘴，迈开腿。多喝水，多运动，管住嘴，迈开腿。多喝水，多运动，管住嘴，迈开腿。多喝水，多运动，管住嘴，迈开腿。"
                        android:textColor="@color/colorPrimary"
                        android:textSize="10sp" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chartHeartRate"
                        android:layout_width="match_parent"
                        android:layout_height="200dp" />

                    <TextView
                        android:id="@+id/tvHeartShow"
                        style="@style/ChartLabel"
                        android:text="心率曲线图" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chartSpeed"
                        android:layout_width="match_parent"
                        android:layout_height="200dp" />

                    <TextView
                        style="@style/ChartLabel"
                        android:text="速度曲线图" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chartOxygenConsumption"
                        android:layout_width="match_parent"
                        android:layout_height="200dp" />

                    <TextView
                        style="@style/ChartLabel"
                        android:text="耗氧曲线图" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chartBreath"
                        android:layout_width="match_parent"
                        android:layout_height="200dp" />

                    <TextView
                        android:id="@+id/tvBreathShow"
                        style="@style/ChartLabel"
                        android:text="呼吸曲线图" />
                </LinearLayout>
            </ScrollView>

            <RelativeLayout
                android:id="@+id/rlUserList"
                android:layout_width="130dp"
                android:layout_height="match_parent"
                android:background="@drawable/shape_with_border"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="8dp"
                    android:text="成员排名"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <View
                    android:id="@+id/line"
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_below="@id/tvLabel"
                    android:background="@color/colorPrimary" />

                <com.drake.statelayout.StateLayout
                    android:id="@+id/state"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@id/line">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_room_user" />
                </com.drake.statelayout.StateLayout>
            </RelativeLayout>

        </LinearLayout>
    </LinearLayout>

</layout>