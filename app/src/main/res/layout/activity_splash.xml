<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        tools:context=".ui.splash.SplashActivity">

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:contentDescription="@null"
            android:src="@drawable/ic_main_logo"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.48"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivLoading"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:contentDescription="@null"
            android:src="@drawable/ic_main_loading"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="h,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.48"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>