<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:background="@drawable/bg_main"
        tools:context="com.ygxj.mcs.ui.main.DataFragment">

        <LinearLayout
            android:id="@+id/llTeamType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_train_type_title"
            android:orientation="horizontal">

            <Space
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5" />

            <TextView
                android:id="@+id/tvPersonal"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:alpha="1"
                android:gravity="center"
                android:text="个人数据"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvTeam"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:alpha="0.5"
                android:gravity="center"
                android:text="团体数据"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <Space
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5" />
        </LinearLayout>

        <com.qmuiteam.qmui.widget.QMUIViewPager
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/shape_with_border_and_radius" />

    </LinearLayout>

</layout>