<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_music_player"
        tools:context=".page.module.relax.music.PlayerActivity">

        <RelativeLayout
            android:id="@+id/llTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@drawable/bg_train_top"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:contentDescription="@null"
                android:padding="15dp"
                android:src="@drawable/ic_return" />

            <TextView
                android:id="@+id/tvUserName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toEndOf="@id/ivBack"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:text="运动者：--" />

            <TextView
                android:id="@+id/tvMusicName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="音乐名称" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_train_bottom"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ivPlay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:contentDescription="@null"
                android:src="@drawable/selector_music_play" />

            <TextView
                android:id="@+id/tvPlayedTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="00:00"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <SeekBar
                android:id="@+id/seekBar"
                style="@style/progress_bar_style"
                android:layout_width="0dp"
                android:layout_height="12dp"
                android:layout_weight="1"
                android:max="100"
                android:progress="0"
                android:thumb="@drawable/progress_point" />

            <TextView
                android:id="@+id/tvTotalTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:text="00:00"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>

    </RelativeLayout>


</layout>