<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="ContentDescription">

        <com.ygxj.mcs.other.PressAlphaImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@null"
            android:padding="15dp"
            android:src="@drawable/ic_return" />

        <ImageView
            android:id="@+id/ivTitleLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/ivBack"
            android:layout_centerHorizontal="true"
            android:src="@drawable/ic_home_title" />

        <com.ygxj.mcs.ui.view.LogoView
            android:id="@+id/ivShowLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="8dp" />
    </RelativeLayout>
</layout>
