<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data />

    <com.hjq.shape.layout.ShapeLinearLayout
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:shape_radius="2dp"
        app:shape_solidColor="@color/white">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="20dp"
            android:text="VR设备IP地址配置"
            android:textSize="12sp" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_ip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="10dp"
            android:background="@color/white"
            android:hint="示例:*************"
            android:inputType="text|number"
            android:textSize="12sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="2dp"
            android:background="@color/lightestBlack" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="取消"
                android:textColor="#1b88ee"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:text="确定"
                android:textColor="#1b88ee"
                android:textSize="12sp" />
        </LinearLayout>
    </com.hjq.shape.layout.ShapeLinearLayout>
</layout>