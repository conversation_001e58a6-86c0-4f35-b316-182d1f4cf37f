<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.base.CopyActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.drake.statelayout.StateLayout
            android:id="@+id/state"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="4"
                    tools:listitem="@layout/item_scale_list" />
            </LinearLayout>

        </com.drake.statelayout.StateLayout>
    </LinearLayout>

</layout>