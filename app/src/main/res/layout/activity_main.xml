<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        tools:context=".ui.main.MainActivity"
        tools:ignore="ContentDescription">

        <com.ygxj.mcs.ui.view.LogoView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp" />

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="4dp"
            android:drawablePadding="4dp"
            android:paddingStart="10dp"
            android:paddingTop="10dp"
            android:paddingEnd="10dp"
            android:paddingBottom="4dp"
            android:textColor="@color/colorPrimary"
            android:textSize="12sp"
            app:drawableEndCompat="@drawable/ic_user_right"
            app:drawableStartCompat="@drawable/ic_user"
            tools:text="用户名" />

        <LinearLayout
            android:id="@+id/llLogout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tvName"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="10dp"
            android:animateLayoutChanges="true"
            android:background="@drawable/shape_with_border_bg"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="8dp"
                android:paddingTop="4dp"
                android:paddingEnd="8dp"
                android:paddingBottom="4dp"
                android:text="退出登录"
                android:textColor="@color/colorPrimary"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical"
            tools:ignore="HardcodedText">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                android:src="@drawable/ic_home_title" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageButton
                        android:id="@+id/ibEvaluate"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@drawable/bg_home_menu_item"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_menu1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="心理评估"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageButton
                        android:id="@+id/ibTrain"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@drawable/bg_home_menu_item"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_menu2" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="训练中心"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageButton
                        android:id="@+id/ibData"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@drawable/bg_home_menu_item"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_menu3" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="数据中心"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageButton
                        android:id="@+id/ibRelax"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@drawable/bg_home_menu_item"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_menu4" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="放松体验"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageButton
                        android:id="@+id/ibUserCenter"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@drawable/bg_home_menu_item"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_menu5" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="个人中心"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>

</layout>