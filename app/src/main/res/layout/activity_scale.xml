<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.base.CopyActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.drake.statelayout.StateLayout
            android:id="@+id/state"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/llMainView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:orientation="horizontal"
                tools:context=".page.module.evaluate.EvaluateFragment"
                tools:ignore="HardcodedText">

                <LinearLayout
                    android:id="@+id/llScale1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:ignore="ContentDescription"
                    tools:visibility="visible">

                    <ImageButton
                        android:id="@+id/ibScale1"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@null"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_scale1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="心理评估1"
                        android:textColor="@color/textColor"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llScale2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:ignore="ContentDescription"
                    tools:visibility="visible">

                    <ImageButton
                        android:id="@+id/ibScale2"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@null"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_scale2" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="心理评估2"
                        android:textColor="@color/textColor"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llScale3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:ignore="ContentDescription"
                    tools:visibility="visible">

                    <ImageButton
                        android:id="@+id/ibScale3"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@null"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_scale3" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="心理评估3"
                        android:textColor="@color/textColor"
                        android:textSize="14sp" />
                </LinearLayout>

            </LinearLayout>
        </com.drake.statelayout.StateLayout>
    </LinearLayout>

</layout>