<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context=".ui.main.MainPageActivity"
        tools:ignore="ContentDescription">

        <com.ygxj.mcs.ui.view.TitleView
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.qmuiteam.qmui.widget.QMUIViewPager
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.angcyo.tablayout.DslTabLayout
            android:id="@+id/tab_layout"
            android:layout_width="350dp"
            android:layout_height="48dp"
            android:layout_gravity="center_horizontal"
            android:background="#42000000"
            android:elevation="8dp"
            app:tab_item_is_equ_width="true">

            <LinearLayout
                android:id="@+id/llMenuEvaluate"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/selector_home_tab"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="2dp">

                <ImageView
                    android:layout_width="21dp"
                    android:layout_height="21dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/ic_home_page1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="心理评估"
                    android:textColor="@color/colorPrimary"
                    android:textSize="10sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llMenuTrain"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/selector_home_tab"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="2dp">

                <ImageView
                    android:layout_width="21dp"
                    android:layout_height="21dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/ic_home_page2" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="训练中心"
                    android:textColor="@color/colorPrimary"
                    android:textSize="10sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llMenuData"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/selector_home_tab"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="2dp">

                <ImageView
                    android:layout_width="21dp"
                    android:layout_height="21dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/ic_home_page3" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="数据中心"
                    android:textColor="@color/colorPrimary"
                    android:textSize="10sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llMenuRelax"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/selector_home_tab"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="2dp">

                <ImageView
                    android:layout_width="21dp"
                    android:layout_height="21dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/ic_home_page4" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="放松体验"
                    android:textColor="@color/colorPrimary"
                    android:textSize="10sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llMenuUserCenter"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/selector_home_tab"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="2dp">

                <ImageView
                    android:layout_width="21dp"
                    android:layout_height="21dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/ic_home_page5" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="个人中心"
                    android:textColor="@color/colorPrimary"
                    android:textSize="10sp" />
            </LinearLayout>
        </com.angcyo.tablayout.DslTabLayout>

    </LinearLayout>

</layout>