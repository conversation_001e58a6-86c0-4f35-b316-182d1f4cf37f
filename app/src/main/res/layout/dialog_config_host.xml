<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="enableSave"
            type="Boolean" />

    </data>

    <com.hjq.shape.layout.ShapeLinearLayout
        app:shape_solidColor="@color/white"
        app:shape_radius="10dp"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:text="配置地址"
            android:textColor="@color/black"
            android:textSize="12sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:lineSpacingMultiplier="1.1"
            android:text="说明: \n1. 请参照示例格式，输入完整的主机地址。\n2. 地址中的符号应使用英文符号。"
            android:textColor="@color/black"
            android:textSize="10sp" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <com.hjq.shape.view.ShapeEditText
                android:id="@+id/etHost"
                android:layout_width="match_parent"
                android:layout_height="26dp"
                android:paddingHorizontal="5dp"
                android:paddingVertical="0dp"
                android:singleLine="true"
                android:text="http://"
                android:textColor="@color/black"
                android:textSize="12sp"
                app:shape_solidColor="@color/_xpopup_list_divider" />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:lineSpacingMultiplier="1.1"
            android:text="示例: http://**************:9124"
            android:textColor="@color/lightest_black"
            android:textSize="10sp" />

        <com.hjq.shape.view.ShapeButton
            android:id="@+id/btnSave"
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_marginHorizontal="80dp"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="保存"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:shape_radius="4dp"
            app:shape_solidColor="#144c8c"
            app:shape_solidDisabledColor="@color/lightest_black"
            app:shape_solidPressedColor="#073367"
            tools:enabled="true" />


    </com.hjq.shape.layout.ShapeLinearLayout>
</layout>