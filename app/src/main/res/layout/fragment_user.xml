<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:gravity="center"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.main.UserFragment">

        <LinearLayout
            android:layout_width="412dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_scale_detail"
            android:orientation="vertical"
            tools:context="com.ygxj.mcs.ui.main.UserFragment">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="10dp">

                <Button
                    android:id="@+id/btnUserInfo"
                    android:layout_width="65dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/selector_button"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="个人信息"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btnUserPwd"
                    android:layout_width="65dp"
                    android:layout_height="24dp"
                    android:background="@drawable/selector_button"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="修改密码"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@drawable/scale_detail_line" />

            <LinearLayout
                android:id="@+id/llUserInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:baselineAligned="false"
                    android:orientation="horizontal"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    tools:ignore="HardcodedText">

                    <RelativeLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:paddingStart="25dp"
                        android:paddingEnd="5dp">

                        <TextView
                            android:id="@+id/userNameLabel"
                            style="@style/UserInfoLabel"
                            android:layout_alignEnd="@id/userBirthDateLabel"
                            android:paddingTop="4dp"
                            android:paddingBottom="4dp"
                            android:text="用户名：" />

                        <TextView
                            android:id="@+id/tvUserName"
                            style="@style/UserInfoLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_toEndOf="@id/userNameLabel"
                            android:text="admin" />

                        <TextView
                            android:id="@+id/userSexLabel"
                            style="@style/UserInfoLabel"
                            android:layout_below="@id/userNameLabel"
                            android:layout_alignEnd="@id/userNameLabel"
                            android:layout_marginTop="6dp"
                            android:text="性别：" />

                        <RadioGroup
                            android:id="@+id/rgSex"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/userSexLabel"
                            android:layout_alignBottom="@id/userSexLabel"
                            android:layout_toEndOf="@id/userSexLabel"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <RadioButton
                                android:id="@+id/rbMale"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:button="@drawable/selector_radio_button"
                                android:paddingStart="5dp"
                                android:paddingEnd="0dp"
                                android:text="男"
                                android:textColor="@color/textColor"
                                android:textSize="12sp" />

                            <RadioButton
                                android:id="@+id/rbFemale"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:button="@drawable/selector_radio_button"
                                android:paddingStart="5dp"
                                android:paddingEnd="0dp"
                                android:text="女"
                                android:textColor="@color/textColor"
                                android:textSize="12sp" />

                        </RadioGroup>

                        <TextView
                            android:id="@+id/userBirthDateLabel"
                            style="@style/UserInfoLabel"
                            android:layout_below="@id/userSexLabel"
                            android:layout_marginTop="6dp"
                            android:text="出生日期：" />

                        <TextView
                            android:id="@+id/tvBirthDate"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/userBirthDateLabel"
                            android:layout_alignBottom="@id/userBirthDateLabel"
                            android:layout_toEndOf="@id/userBirthDateLabel"
                            android:background="@drawable/ic_input_bg_small"
                            android:gravity="center_vertical"
                            android:hint="请输入出生日期"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:textColor="@color/textColor"
                            android:textColorHint="@color/lightestBlack"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/userPhoneLabel"
                            style="@style/UserInfoLabel"
                            android:layout_below="@id/userBirthDateLabel"
                            android:layout_marginTop="6dp"
                            android:text="联系电话：" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/etPhone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/userPhoneLabel"
                            android:layout_alignBottom="@id/userPhoneLabel"
                            android:layout_toEndOf="@id/userBirthDateLabel"
                            android:background="@drawable/ic_input_bg_small"
                            android:gravity="center_vertical"
                            android:hint="请输入联系电话"
                            android:inputType="number"
                            android:maxLines="1"
                            android:paddingStart="4dp"
                            android:paddingEnd="8dp"
                            android:singleLine="true"
                            android:textColor="@color/textColor"
                            android:textColorHint="@color/lightestBlack"
                            android:textSize="12sp" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:paddingStart="5dp"
                        android:paddingEnd="25dp">

                        <TextView
                            android:id="@+id/userAgeLabel"
                            style="@style/UserInfoLabel"
                            android:layout_alignEnd="@id/userDepartmentLabel"
                            android:text="年龄：" />

                        <TextView
                            android:id="@+id/tvAge"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/userAgeLabel"
                            android:layout_alignBottom="@id/userAgeLabel"
                            android:layout_toEndOf="@id/userAgeLabel"
                            android:background="@drawable/ic_input_bg_small"
                            android:gravity="center_vertical"
                            android:hint="请选择生日"
                            android:maxLines="1"
                            android:paddingStart="4dp"
                            android:paddingEnd="8dp"
                            android:singleLine="true"
                            android:textColor="@color/textColor"
                            android:textColorHint="@color/lightestBlack"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/userDepartmentLabel"
                            style="@style/UserInfoLabel"
                            android:layout_below="@id/userAgeLabel"
                            android:layout_marginTop="6dp"
                            android:text="所属部门：" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/etDepartment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/userDepartmentLabel"
                            android:layout_alignBottom="@id/userDepartmentLabel"
                            android:layout_toEndOf="@id/userDepartmentLabel"
                            android:background="@drawable/ic_input_bg_small"
                            android:gravity="center_vertical"
                            android:hint="请输入所属部门"
                            android:maxLines="1"
                            android:paddingStart="4dp"
                            android:paddingEnd="8dp"
                            android:singleLine="true"
                            android:textColor="@color/textColor"
                            android:textColorHint="@color/lightestBlack"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/userMailLabel"
                            style="@style/UserInfoLabel"
                            android:layout_below="@id/userDepartmentLabel"
                            android:layout_alignEnd="@id/userDepartmentLabel"
                            android:layout_marginTop="6dp"
                            android:text="邮箱：" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/etMail"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/userMailLabel"
                            android:layout_alignBottom="@id/userMailLabel"
                            android:layout_toEndOf="@id/userMailLabel"
                            android:background="@drawable/ic_input_bg_small"
                            android:gravity="center_vertical"
                            android:hint="请输入您的邮箱"
                            android:maxLines="1"
                            android:paddingStart="4dp"
                            android:paddingEnd="8dp"
                            android:singleLine="true"
                            android:textColor="@color/textColor"
                            android:textColorHint="@color/lightestBlack"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/userAddressLabel"
                            style="@style/UserInfoLabel"
                            android:layout_below="@id/userMailLabel"
                            android:layout_alignEnd="@id/userDepartmentLabel"
                            android:layout_marginTop="6dp"
                            android:text="地址：" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/etAddress"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/userAddressLabel"
                            android:layout_alignBottom="@id/userAddressLabel"
                            android:layout_toEndOf="@id/userAddressLabel"
                            android:background="@drawable/ic_input_bg_small"
                            android:gravity="center_vertical"
                            android:hint="请输入您的地址"
                            android:maxLines="1"
                            android:paddingStart="4dp"
                            android:paddingEnd="8dp"
                            android:singleLine="true"
                            android:textColor="@color/textColor"
                            android:textColorHint="@color/lightestBlack"
                            android:textSize="12sp" />

                    </RelativeLayout>

                </LinearLayout>

                <Button
                    android:id="@+id/btnSaveUserInfo"
                    android:layout_width="89dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ic_login_btn"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="保存"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llUserPwd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/UserInfoLabel"
                        android:layout_width="90dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="原密码：" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etOldPwd"
                        android:layout_width="150dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ic_input_bg_small"
                        android:gravity="center_vertical"
                        android:hint="请输入原密码"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:paddingStart="4dp"
                        android:paddingEnd="8dp"
                        android:singleLine="true"
                        android:textColor="@color/textColor"
                        android:textColorHint="@color/lightestBlack"
                        android:textSize="12sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/UserInfoLabel"
                        android:layout_width="90dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="新密码：" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etNewPwd"
                        android:layout_width="150dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ic_input_bg_small"
                        android:gravity="center_vertical"
                        android:hint="请输入新密码"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:paddingStart="4dp"
                        android:paddingEnd="8dp"
                        android:singleLine="true"
                        android:textColor="@color/textColor"
                        android:textColorHint="@color/lightestBlack"
                        android:textSize="12sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/UserInfoLabel"
                        android:layout_width="90dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="确认新密码：" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etNewPwdConfirm"
                        android:layout_width="150dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ic_input_bg_small"
                        android:gravity="center_vertical"
                        android:hint="请再次输入新密码"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:paddingStart="4dp"
                        android:paddingEnd="8dp"
                        android:singleLine="true"
                        android:textColor="@color/textColor"
                        android:textColorHint="@color/lightestBlack"
                        android:textSize="12sp" />

                </LinearLayout>

                <Button
                    android:id="@+id/btnSavePwd"
                    android:layout_width="89dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/ic_login_btn"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="保存"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


</layout>