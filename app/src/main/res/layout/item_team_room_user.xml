<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.mcs.http.model.RoomUsersStateData" />
    </data>

    <LinearLayout
        android:layout_margin="@dimen/dp_10"
        android:id="@+id/item"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@drawable/shape_with_bg"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="90dp">

            <androidx.core.widget.ContentLoadingProgressBar
                android:id="@+id/progressBar"
                style="@style/Base.Widget.AppCompat.ProgressBar"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:layout_marginStart="5dp"
                android:max="100"
                android:progress="50"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvUserName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                tools:text="admin"
                android:text="@{m.uname}"
                android:textColor="@color/colorPrimary"
                android:textSize="10sp" />

            <ImageView
                android:id="@+id/ivReady"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:contentDescription="@null"
                android:src="@drawable/ic_check_circle_24dp"
                android:visibility="gone"
                tools:visibility="visible" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginStart="5dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="5dp"
            android:background="@color/textColor" />

        <TextView
            android:id="@+id/tvState"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            tools:text="未准备"
            android:textColor="@color/white"
            android:textSize="10sp" />

    </LinearLayout>
</layout>
