<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/rl_content"
            android:layout_width="120dp"
            android:layout_height="60dp"
            android:background="@color/white"
            android:visibility="gone"
            tools:visibility="visible">

            <com.ygxj.mcs.other.PressAlphaImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:src="@drawable/icon_close_blue" />

            <TextView
                android:id="@+id/music_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_10"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/colorPrimary"
                android:textSize="10sp"
                tools:text="音乐标题" />

            <LinearLayout
                android:id="@+id/ll_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/music_name"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="2dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivPreview"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_music_previous_blue" />

                <ImageView
                    android:id="@+id/ivPlayPause"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginHorizontal="5dp"
                    android:src="@drawable/ic_music_pause_blue" />

                <ImageView
                    android:id="@+id/ivNext"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_music_next_blue" />

            </LinearLayout>

        </RelativeLayout>

        <com.hjq.shape.layout.ShapeRelativeLayout
            android:id="@+id/rl_music"
            android:layout_width="20dp"
            android:layout_height="60dp"
            app:shape_radiusInBottomRight="4dp"
            app:shape_solidColor="#107abd"
            app:shape_radiusInTopRight="4dp">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@drawable/ic_music_white" />
        </com.hjq.shape.layout.ShapeRelativeLayout>
    </LinearLayout>


</layout>