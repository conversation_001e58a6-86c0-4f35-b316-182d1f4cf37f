<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        android:orientation="vertical"
        tools:context="com.ygxj.mcs.ui.train.DeviceManagerActivity">

        <com.ygxj.mcs.ui.view.TitleView
            android:id="@+id/titleView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            tools:ignore="HardcodedText">

            <LinearLayout
                android:id="@+id/llDeviceContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="vertical"
                tools:ignore="SmallSp"
                tools:visibility="visible">

                <Button
                    android:id="@+id/btnClearHistory"
                    style="@style/btnClear"
                    android:layout_gravity="end"
                    android:text="清除历史设备"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <RelativeLayout
                    android:id="@+id/rlFingerDevice"
                    style="@style/deviceContainer">

                    <TextView
                        android:id="@+id/finger"
                        style="@style/deviceType"
                        android:text="指脉设备" />

                    <TextView
                        android:id="@+id/tvFingerDeviceName"
                        style="@style/deviceName"
                        android:layout_below="@id/finger"
                        tools:text="BerryMed" />

                    <TextView
                        android:id="@+id/tvFingerDeviceState"
                        style="@style/deviceState"
                        android:layout_toStartOf="@id/pbFingerDeviceState"
                        android:text="正在连接" />

                    <androidx.core.widget.ContentLoadingProgressBar
                        android:id="@+id/pbFingerDeviceState"
                        style="@style/deviceProgress" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rlBikeDevice"
                    style="@style/deviceContainer">

                    <TextView
                        android:id="@+id/bike"
                        style="@style/deviceType"
                        android:text="单车设备" />

                    <TextView
                        android:id="@+id/tvBikeDeviceName"
                        style="@style/deviceName"
                        android:layout_below="@id/bike"
                        tools:text="WD1902" />

                    <TextView
                        android:id="@+id/tvBikeTip"
                        style="@style/deviceName"
                        android:layout_centerInParent="true"
                        android:visibility="gone"
                        tools:text="提示：请骑行几圈，激活设备"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvBikeDeviceState"
                        style="@style/deviceState"
                        android:layout_toStartOf="@id/pbBikeDeviceState"
                        android:text="正在连接" />

                    <androidx.core.widget.ContentLoadingProgressBar
                        android:id="@+id/pbBikeDeviceState"
                        style="@style/deviceProgress" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rlBreathDevice"
                    style="@style/deviceContainer">

                    <TextView
                        android:id="@+id/breath"
                        style="@style/deviceType"
                        android:text="呼吸设备" />

                    <TextView
                        android:id="@+id/tvBreathDeviceName"
                        style="@style/deviceName"
                        android:layout_below="@id/breath"
                        tools:text="HX009" />

                    <TextView
                        android:id="@+id/tvBreathDeviceState"
                        style="@style/deviceState"
                        android:layout_toStartOf="@id/pbBreathDeviceState"
                        android:text="正在连接" />

                    <androidx.core.widget.ContentLoadingProgressBar
                        android:id="@+id/pbBreathDeviceState"
                        style="@style/deviceProgress" />
                </RelativeLayout>
            </LinearLayout>

            <!--            <include-->
            <!--                android:visibility="gone"-->
            <!--                tools:visibility="visible"-->
            <!--                android:id="@+id/loading"-->
            <!--                layout="@layout/layout_loading" />-->

            <TextView
                android:id="@+id/tvState"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:lineSpacingExtra="2dp"
                android:padding="5dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:text="未找到设备，请重新搜索" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnSearchDevice"
                style="@style/btnCommon"
                android:text="搜索设备" />

            <Button
                android:id="@+id/btnGoingTrain"
                style="@style/btnCommon"
                android:layout_marginStart="20dp"
                android:text="去训练" />
        </LinearLayout>
    </LinearLayout>

</layout>