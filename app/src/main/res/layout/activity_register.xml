<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main"
        tools:context=".ui.register.RegisterActivity"
        tools:ignore="ContentDescription">

        <com.ygxj.mcs.ui.view.TitleView
            android:id="@+id/titleView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical"
            tools:ignore="HardcodedText">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_login_title" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:background="@drawable/ic_login_background"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:background="@drawable/ic_login_bg"
                    android:gravity="center"
                    android:text="注册"
                    android:textColor="#00A8E7"
                    android:textSize="16sp" />

                <LinearLayout
                    android:id="@+id/llForm"
                    android:layout_width="202dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tvTitle"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="15dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ic_login_input_bg"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_login_user" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/etName"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:background="@null"
                            android:hint="请输入用户名"
                            android:inputType="text"
                            android:maxLines="1"
                            android:minHeight="0dp"
                            android:padding="2dp"
                            android:textColor="@color/colorPrimary"
                            android:textColorHint="#4584C4"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="23dp"
                        android:layout_marginTop="10dp"
                        android:background="@drawable/ic_login_input_bg"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_login_password" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/etPwd"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:background="@null"
                            android:hint="请输入密码"
                            android:inputType="textPassword"
                            android:maxLines="1"
                            android:minHeight="0dp"
                            android:padding="2dp"
                            android:textColor="@color/colorPrimary"
                            android:textColorHint="#4584C4"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="23dp"
                        android:layout_marginTop="10dp"
                        android:background="@drawable/ic_login_input_bg"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_login_password" />

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/etConfirmPwd"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:background="@null"
                            android:hint="请再次输入密码"
                            android:inputType="textPassword"
                            android:maxLines="1"
                            android:minHeight="0dp"
                            android:padding="2dp"
                            android:textColor="@color/colorPrimary"
                            android:textColorHint="#4584C4"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>


                <Button
                    android:id="@+id/btnRegister"
                    android:layout_width="89dp"
                    android:layout_height="25dp"
                    android:layout_below="@id/llForm"
                    android:layout_centerInParent="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/ic_login_btn"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:text="注册"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </RelativeLayout>
        </LinearLayout>
    </RelativeLayout>
</layout>