<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.mcs.http.model.OnlineUsersData" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:background="#000">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:maxEms="9"
                android:maxLines="1"
                android:ellipsize="end"
                android:id="@+id/tvUserName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:text="@{m.userName}"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:text="参与者姓名" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="8dp">

                <TextView
                    android:id="@+id/tvInviteState"
                    android:layout_width="44dp"
                    android:layout_height="19dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/btn_invite"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="8sp"
                    tools:text="已邀请" />

                <androidx.core.widget.ContentLoadingProgressBar
                    android:id="@+id/progressBar"
                    style="@style/deviceProgress"
                    android:layout_marginEnd="18dp"
                    tools:visibility="visible" />
            </RelativeLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@drawable/shape_with_bg" />
    </LinearLayout>
</layout>
