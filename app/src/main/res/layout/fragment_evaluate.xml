<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@drawable/bg_main"
        tools:context="com.ygxj.mcs.ui.main.EvaluateFragment">

        <com.drake.statelayout.StateLayout
            android:id="@+id/state"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/llMainView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:orientation="horizontal"
                tools:context=".page.module.evaluate.EvaluateFragment"
                tools:ignore="HardcodedText">

                <LinearLayout
                    android:id="@+id/llScale1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    tools:ignore="ContentDescription"
                    tools:visibility="visible">

                    <ImageButton
                        android:id="@+id/ibScale1"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@null"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_scale1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="量表评估"
                        android:textColor="@color/textColor"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llScale2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    tools:ignore="ContentDescription"
                    tools:visibility="visible">

                    <ImageButton
                        android:id="@+id/ibScale2"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@null"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_scale2" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="压力评估"
                        android:textColor="@color/textColor"
                        android:textSize="14sp" />
                </LinearLayout>

            </LinearLayout>
        </com.drake.statelayout.StateLayout>

    </RelativeLayout>
</layout>