<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@drawable/bg_main"
        tools:context="com.ygxj.mcs.ui.main.TrainFragment">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ibTiXinTrain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                tools:ignore="ContentDescription"
                tools:visibility="visible">

                <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/btn_tixin2"
                    android:scaleType="centerCrop"
                    app:qmui_corner_radius="5dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="体心训练"
                    android:textColor="@color/textColor"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ibShenXinTrain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                tools:ignore="ContentDescription"
                tools:visibility="visible">

                <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginStart="40dp"
                    android:layout_marginEnd="40dp"
                    android:background="@drawable/btn_shenxin2"
                    android:scaleType="centerCrop"
                    app:qmui_corner_radius="5dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="身心训练"
                    android:textColor="@color/textColor"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ibSystemTrain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                tools:ignore="ContentDescription"
                tools:visibility="visible">

                <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/btn_system2"
                    android:scaleType="centerCrop"
                    app:qmui_corner_radius="5dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="系统推荐"
                    android:textColor="@color/textColor"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ivVRGameTrain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="40dp"
                android:gravity="center"
                android:orientation="vertical"
                tools:ignore="ContentDescription"
                tools:visibility="visible">

                <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/btn_vr_game"
                    android:scaleType="centerCrop"
                    app:qmui_corner_radius="5dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="VR游戏场景"
                    android:textColor="@color/textColor"
                    android:textSize="14sp" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llVrIpConfigView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_margin="10dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvVrIp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/textColor"
                android:textSize="12sp"
                tools:text="当前VR设备地址：未配置" />

            <TextView
                android:id="@+id/tvConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="修改配置"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>

    </RelativeLayout>
</layout>