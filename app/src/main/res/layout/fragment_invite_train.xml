<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>


    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@drawable/bg_main"
        tools:context="com.ygxj.mcs.ui.train.InviteTrainFragment">

        <LinearLayout
            android:id="@+id/llMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/btnTrainStart"
            android:layout_marginStart="40dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="40dp"
            android:layout_marginBottom="10dp"
            android:baselineAligned="false"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1.5"
                android:background="@drawable/shape_with_bg"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="训练时长:"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tvTrainDuration"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:background="@drawable/shape_with_border_and_radius"
                        android:hint="请选择时长"
                        android:padding="6dp"
                        android:textColor="@color/white"
                        android:textColorHint="@color/textColor"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="训练场景:"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tvTrainScene"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:background="@drawable/shape_with_border_and_radius"
                        android:hint="请选择训练场景"
                        android:padding="6dp"
                        android:textColor="@color/white"
                        android:textColorHint="@color/textColor"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="训练指标:"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <SeekBar
                        android:id="@+id/seekBar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:max="100"
                        android:progress="80" />

                    <TextView
                        android:id="@+id/tvProgress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="80%"
                        android:textColor="@color/white"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_with_border"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="8dp"
                    android:text="在线用户"
                    android:textColor="@color/colorPrimary"
                    android:textSize="11sp" />

                <View
                    android:id="@+id/line"
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_below="@id/tvLabel"
                    android:background="@color/colorPrimary" />

                <com.drake.statelayout.StateLayout
                    android:id="@+id/state"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@id/line">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:listitem="@layout/item_online_user"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
                </com.drake.statelayout.StateLayout>

            </RelativeLayout>

        </LinearLayout>

        <Button
            android:id="@+id/btnTrainStart"
            style="@style/btnCommon"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="10dp"
            android:text="创建房间" />

    </RelativeLayout>
</layout>