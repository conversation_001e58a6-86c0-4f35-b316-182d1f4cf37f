<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.mcs.http.model.ScaleListData" />
    </data>

    <LinearLayout
        android:id="@+id/item"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:foreground="?selectableItemBackgroundBorderless"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingHorizontal="10dp"
        android:paddingVertical="5dp"
        tools:ignore="UnusedAttribute">

        <ImageView
            android:id="@+id/ivScale"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:background="@null"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_scale1" />

        <TextView
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@{m.MC}"
            android:textColor="@color/textColor"
            android:textSize="14sp"
            tools:text="心理评估1" />
    </LinearLayout>
</layout>
