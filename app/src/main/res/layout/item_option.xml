<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="m"
            type="com.ygxj.mcs.http.model.OptionsData" />
    </data>

    <LinearLayout
        android:id="@+id/item"
        android:layout_marginVertical="@dimen/dp_3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            selected="@{m.checked}"
            android:id="@+id/iv"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:background="@drawable/selector_check" />

        <TextView
            android:id="@+id/tv_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="@color/scaleTextColor"
            android:textSize="12sp"
            android:text="@{m.option}"
            tools:text="题目" />

    </LinearLayout>
</layout>
