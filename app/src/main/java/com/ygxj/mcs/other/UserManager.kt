package com.ygxj.mcs.other

import com.tencent.mmkv.MMKV
import com.ygxj.mcs.http.model.UserData


/**
 * 用户配置信息
 */
object UserManager {
    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("UserManager")
    }

    /**
     * 是否记住密码
     */
    var rememberPws: Boolean
        get() = mmkv.decodeBool("rememberPws", false)
        set(value) {
            mmkv.encode("rememberPws", value)
        }

    /**
     * 用户名
     */
    var name: String?
        get() = mmkv.decodeString("name", "")
        set(value) {
            mmkv.encode("name", value)
        }

    /**
     * 用户ID
     */
    var id: Int
        get() = mmkv.decodeInt("id", 0)
        set(value) {
            mmkv.encode("id", value)
        }

    /**
     * 用户性别
     */
    var sex: String?
        get() = mmkv.decodeString("sex", "")
        set(value) {
            mmkv.encode("sex", value)
        }

    /**
     * 用户年龄
     */
    var age: String?
        get() = mmkv.decodeString("age", "")
        set(value) {
            mmkv.encode("age", value)
        }

    /**
     * 用户生日
     */
    var birthday: String?
        get() = mmkv.decodeString("birthday", "")
        set(value) {
            mmkv.encode("birthday", value)
        }

    /**
     * 用户电话
     */
    var phone: String?
        get() = mmkv.decodeString("phone", "")
        set(value) {
            mmkv.encode("phone", value)
        }

    /**
     * 用户部门
     */
    var department: String?
        get() = mmkv.decodeString("department", "")
        set(value) {
            mmkv.encode("department", value)
        }

    /**
     * 用户邮箱
     */
    var email: String?
        get() = mmkv.decodeString("email", "")
        set(value) {
            mmkv.encode("email", value)
        }

    /**
     * 用户地址
     */
    var address: String?
        get() = mmkv.decodeString("address", "")
        set(value) {
            mmkv.encode("address", value)
        }


    /**
     * 设置默认用户数据
     */
    fun setDefaultUserData(userData: UserData) {
        id = userData.ID
        name = userData.UserName
        sex = userData.Sex.trim()
        if (userData.Age!=0){
            age = userData.Age.toString()
        }
        birthday = userData.birthdayTime
        phone = userData.Telephone
        department = userData.department
        email = userData.Eml
        address = userData.address

    }

    /**
     * 清除用户信息
     */
    fun clearUserData() {
        mmkv.clearAll()
    }
}