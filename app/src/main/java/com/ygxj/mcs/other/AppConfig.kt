package com.ygxj.mcs.other

import com.tencent.mmkv.MMKV

/**
 * 心率计算方法规则如下（常规版本）
 * 假设：用户年龄20岁，训练时长15分钟，在运动时，将心率控制在最大心率的60%到80%之间（60%和80%是后台配置的参数，从后台获取），可以达到最佳的锻炼效果。
 * 计算：
 * 根据年龄计算出用户达标心率区间：116.1至154.8
 * 最大心率计算方式为 206.9-(0.67 * 20)
 * 最大心率*0.6=116.1
 * 最大心率*0.8=154.8
 *
 * 合格时长计算方式，假设训练总时长为15分钟，合格时间为总时长的3分之1，既5分钟
 * 15*1/3 = 5分钟
 * 在训练过程中，用户的实时心率处于116.1至154.8之间的【累计】时长达5分钟，即视为训练合格
 */
object AppConfig {
    /**
     * 是否开放VR功能
     * 如果关闭,App中所有VR相关的功能都会隐藏
     */
    const val enableVr = true

    /**
     * 是否显示训练实时图表
     */
    const val enableChart = true

    /**
     * 是否显示团体相关
     */
    const val enableTeam = false

    /**
     * 是否显示阳光心健logo
     * app里的logo会自动适配,app图标需要在 Manifest 中手动更改
     */
    const val SHOW_YGXJ_LOGO = true

    /**
     * 是否启用新的心率计算方法规则如下
     * 假设：用户年龄20岁，训练时长15分钟，在运动时，将心率控制在最大心率的60%到85%之间（60%和85%是app写死的，不受后台设置的参数影响），可以达到最佳的锻炼效果。
     * 计算方式如下：
     * 根据年龄计算出用户达标心率区间：99.63至137.7
     * (220-年龄)*0.6*0.9*0.9=99.63
     * (220-年龄)*0.85*0.9*0.9=137.7
     *
     * 合格时长计算方式，假设训练总时长为15分钟，则合格时长为8.1分钟，计算方式如下
     * 15*2/3*0.9*0.9 = 8.1分钟
     * 在训练过程中，用户的实时心率处于99.63至137.7之间的【累计】时长达8.1分钟，即视为训练合格
     */
    const val ENABLE_NEW_HEART_RATE = false  //没有特殊要求 不开启

}