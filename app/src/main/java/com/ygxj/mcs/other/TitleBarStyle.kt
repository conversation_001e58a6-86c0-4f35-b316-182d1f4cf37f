package com.ygxj.mcs.other

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.ConvertUtils
import com.hjq.bar.style.LightBarStyle
import com.ygxj.mcs.R

/**
 * 标题栏初始器
 */
class TitleBarStyle : LightBarStyle() {

    override fun newTitleView(context: Context): TextView {
        return AppCompatTextView(context)
    }

    override fun newLeftView(context: Context): TextView {
        return PressAlphaTextView(context)
    }

    override fun newRightView(context: Context): TextView {
        return PressAlphaTextView(context)
    }

    override fun getTitleBarBackground(context: Context): Drawable {
        return ColorDrawable(ContextCompat.getColor(context, R.color.white))
    }

    override fun getBackButtonDrawable(context: Context): Drawable? {
        return ContextCompat.getDrawable(context, R.drawable.ic_back_white)
    }

    override fun getLeftTitleBackground(context: Context): Drawable? {
        return null
    }

    override fun getRightTitleBackground(context: Context): Drawable? {
        return null
    }

    override fun getChildVerticalPadding(context: Context): Int {
        return ConvertUtils.dp2px(14F)
    }

    override fun getTitleSize(context: Context): Float {
        return ConvertUtils.sp2px(16F).toFloat()
    }

    override fun getTitleColor(context: Context?): ColorStateList {
        return ColorStateList.valueOf(Color.WHITE)
    }

    override fun getLeftTitleSize(context: Context): Float {
        return ConvertUtils.sp2px(13F).toFloat()
    }

    override fun getLeftIconWidth(context: Context?): Int {
        return ConvertUtils.dp2px(10F)
    }

    override fun getRightTitleSize(context: Context): Float {
        return ConvertUtils.sp2px(18F).toFloat()
    }

    override fun getTitleIconPadding(context: Context): Int {
        return ConvertUtils.dp2px(2F)
    }

    override fun getLeftIconPadding(context: Context): Int {
        return ConvertUtils.dp2px(2F)
    }

    override fun getLineSize(context: Context?): Int {
        return 0
    }

    override fun getRightIconPadding(context: Context): Int {
        return ConvertUtils.dp2px(2F)
    }



}