package com.ygxj.mcs.other

import com.tencent.mmkv.MMKV


/**
 * 心理评估信息
 */
object ScaleManager {
    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID(UserManager.id.toString())
    }

    /**
     * 量表id
     */
    var id: String?
        get() = mmkv.decodeString("id", "")
        set(value) {
            mmkv.encode("id", value)
        }

    /**
     * 量表名
     */
    var title: String?
        get() = mmkv.decodeString("title", "")
        set(value) {
            mmkv.encode("title", value)
        }


}