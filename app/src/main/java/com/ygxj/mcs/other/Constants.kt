package com.ygxj.mcs.other

/**
 * 常量
 */
object Constants {
    //接受训练中心发出的消息，在训练之前需要完成心理评估，如果没完成，则跳转到心理评估页面
    const val SWITCH_TO_SCALE = "switchToScale"
}

object TrainType {
    /**
     * 基础训练
     */
    const val NORMAL: Int = 0

    /**
     * 加强训练
     */
    const val STRENGTHEN: Int = 1

    /**
     * 身心训练
     */
    const val SHEN_XIN: Int = 2

    /**
     * 团体训练
     */
    const val TEAM: Int = 3

    /**
     * 自主训练
     */
    const val SELF: Int = 4


    fun getTrainType(type: Int): String {
        return when (type) {
            NORMAL -> "基础训练"
            STRENGTHEN -> "加强训练"
            SHEN_XIN -> "身心训练"
            TEAM -> "团体训练"
            SELF -> "自主训练"
            else -> ""
        }
    }
}

/**
 * 邀请用户状态
 */
object InviteUserState {
    /**
     * 未邀请
     */
    const val UNINVITED: Int = 0
    /**
     * 邀请中
     */
    const val INVITING: Int = 1
    /**
     * 已邀请
     */
    const val INVITED: Int = 2
}