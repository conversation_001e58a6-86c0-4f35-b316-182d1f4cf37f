package com.ygxj.mcs.action

import android.app.Application
import android.content.Context
import com.bhm.ble.BleManager
import com.bhm.ble.attribute.BleOptions
import com.drake.brv.BR
import com.drake.brv.utils.BRV
import com.drake.net.NetConfig
import com.drake.net.interceptor.LogRecordInterceptor
import com.drake.net.okhttp.setConverter
import com.drake.net.okhttp.setDebug
import com.drake.net.okhttp.setDialogFactory
import com.drake.net.okhttp.setErrorHandler
import com.drake.net.okhttp.setRequestInterceptor
import com.drake.statelayout.StateConfig
import com.drake.tooltip.dialog.BubbleDialog
import com.google.gson.reflect.TypeToken
import com.google.gson.stream.JsonToken
import com.hjq.bar.TitleBar
import com.hjq.gson.factory.GsonFactory
import com.hjq.gson.factory.ParseExceptionCallback
import com.hjq.toast.Toaster
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.MaterialHeader
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.tencent.mmkv.MMKV
import com.ygxj.mcs.BuildConfig
import com.ygxj.mcs.R
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.other.GlobalErrorHandler
import com.ygxj.mcs.http.other.GlobalHeaderInterceptor
import com.ygxj.mcs.http.other.GsonConverter
import com.ygxj.mcs.other.ActivityManager
import com.ygxj.mcs.other.DebugLoggerTree
import com.ygxj.mcs.other.TitleBarStyle
import org.litepal.LitePal
import timber.log.Timber
import java.util.concurrent.TimeUnit


class AppApplication : Application() {

    /**
     * 全局context
     */
    companion object {
        lateinit var appContext: Context
    }

    override fun onCreate() {
        super.onCreate()
        appContext = applicationContext
        initializeThirdPart()
    }

    /** 初始化第三方依赖库库 */
    private fun initializeThirdPart() {
        //初始化蓝牙
        BleManager.get().init(
            this,
            BleOptions.Builder()
                .setScanMillisTimeOut(3000)
                .setConnectMillisTimeOut(3000)
                .setMaxConnectNum(3)
                //.setScanDeviceName(FingerDataUtil.fingerName,BikeDataUtil.bikeName,BreathDataUtil.breathName)//只扫描这些设备
                .setConnectRetryCountAndInterval(1, 1000)
                .build()
        )

        // 数据库框架
        LitePal.initialize(this)

        Toaster.init(this)

        // Activity 栈管理初始化
        ActivityManager.getInstance().init(this)

        // 设置标题栏初始化器
        TitleBar.setDefaultStyle(TitleBarStyle())

        // MMKV 初始化
        MMKV.initialize(this)

        // 初始化日志打印
        if (BuildConfig.DEBUG) {
            Timber.plant(DebugLoggerTree())
        }

        //网络请求框架 初始化
        NetConfig.initialize(Api.HOST, this) {

            // 超时设置
            connectTimeout(10, TimeUnit.SECONDS)

            // LogCat是否输出异常日志, 异常日志可以快速定位网络请求错误
            setDebug(BuildConfig.DEBUG)

            // AndroidStudio OkHttp Profiler 插件输出网络日志
            addInterceptor(LogRecordInterceptor(BuildConfig.DEBUG))

            // 添加请求拦截器, 可配置全局/动态参数
            setRequestInterceptor(GlobalHeaderInterceptor())

            // 数据转换器
            setConverter(GsonConverter())

            //错误处理
            setErrorHandler(GlobalErrorHandler())

            // 自定义全局加载对话框
            setDialogFactory {
                BubbleDialog(it,"加载中...").apply {
                    setCancelable(false)
                }
            }
        }

        // 设置 Json 解析容错监听
        GsonFactory.setParseExceptionCallback(object : ParseExceptionCallback {
            override fun onParseObjectException(
                typeToken: TypeToken<*>,
                fieldName: String,
                jsonToken: JsonToken,
            ) {
                Timber.e("解析对象析异常：$typeToken#$fieldName，后台返回的类型为：$jsonToken")
            }

            override fun onParseListItemException(
                typeToken: TypeToken<*>,
                fieldName: String,
                listItemJsonToken: JsonToken,
            ) {
                Timber.e("解析 List 异常：$typeToken#$fieldName，后台返回的条目类型为：$listItemJsonToken")
            }

            override fun onParseMapItemException(
                typeToken: TypeToken<*>,
                fieldName: String,
                mapItemKey: String,
                mapItemJsonToken: JsonToken,
            ) {
                Timber.e("解析 Map 异常：$typeToken#$fieldName，mapItemKey = $mapItemKey，后台返回的条目类型为：$mapItemJsonToken")
            }
        })

        // 全局缺省页配置 [https://github.com/liangjingkanji/StateLayout]
        StateConfig.apply {
            emptyLayout = R.layout.layout_empty
            loadingLayout = R.layout.layout_loading
            errorLayout = R.layout.layout_empty
        }

        // 初始化SmartRefreshLayout, 这是自动下拉刷新和上拉加载采用的第三方库  [https://github.com/scwang90/SmartRefreshLayout/tree/master] V2版本
        SmartRefreshLayout.setDefaultRefreshHeaderCreator { context, _ ->
            MaterialHeader(context)
        }
        SmartRefreshLayout.setDefaultRefreshFooterCreator { context, _ ->
            ClassicsFooter(context)
        }

        //BRV列表配置 详见 https://liangjingkanji.github.io/BRV/
        BRV.modelId = BR.m
    }



}