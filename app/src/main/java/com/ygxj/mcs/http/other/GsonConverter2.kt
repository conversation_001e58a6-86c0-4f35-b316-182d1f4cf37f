package com.ygxj.mcs.http.other

import com.drake.net.convert.JSONConvert
import com.hjq.gson.factory.GsonFactory
import com.ygxj.mcs.http.model.BaseData
import okhttp3.Response
import org.json.JSONObject
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

class GsonConverter2 : JSONConvert(success = "success", code = "status", message = "message") {
    companion object {
        private val gson = GsonFactory.getSingletonGson()
    }

    override fun <R> onConvert(succeed: Type, response: Response): R? {
        return super.onConvert(succeed, response)
    }

    override fun <R> String.parseBody(succeed: Type): R? {
        return if (isBaseDataType(succeed)) {
            gson.fromJson<R>(this, succeed)
        } else {
            val string = try {
                JSONObject(this).getString("result")
            } catch (e: Exception) {
                this
            }
            gson.fromJson<R>(string, succeed)
        }
    }

    /**
     * 判断succeed是不是BaseData类型，如果是就解析全部数据，如果不是就解析data中的数据
     */
    private fun isBaseDataType(type: Type): Boolean {
        if (type is ParameterizedType) {
            val rawType = type.rawType

            if (rawType is Class<*>) {
                if (BaseData::class.java.isAssignableFrom(rawType)) {
                    return true
                }
            }
        }

        return false
    }
}