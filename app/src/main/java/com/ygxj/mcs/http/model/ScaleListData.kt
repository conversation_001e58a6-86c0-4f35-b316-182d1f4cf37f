package com.ygxj.mcs.http.model


/**
 * 量表列表
 */
data class ScaleListData(
    val BZ: String = "", // 指导语：请您根据自身情况如实作答，并请仔细阅读每一道题，根据题目中所叙述的内容和您自己相符合的程度，进行选择。
    val CSXS: Int = 0, // 0
    val FL: Int = 0, // 0
    val FP: Int = 0, // 0
    val Id: Int = 0, // 42
    val IsDisplay: Int = 0, // 0
    val JJ: String = "", // 简介：社会适应能力是一个人适应社会生活和社会环境的能力。社会适应能力的高低，从某种意义上说，表明一个人的成熟程度。具有良好的社会适应能力对于大学生走上社会,谋求生存和发展具有重要意义。
    val MC: String = "", // 社会适应能力诊断量表
    val PX: Int = 0, // 0
    val SetMark: Int = 0, // 0
    val ZT: Int = 0 // 0

)
