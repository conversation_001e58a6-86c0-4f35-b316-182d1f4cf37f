package com.ygxj.mcs.http.other

import com.drake.net.convert.NetConverter
import com.drake.net.exception.ConvertException
import com.drake.net.exception.RequestParamsException
import com.drake.net.exception.ServerResponseException
import com.hjq.gson.factory.GsonFactory
import okhttp3.Response
import java.lang.reflect.Type


class GsonConverter3 : NetConverter {
    companion object {
        private val gson = GsonFactory.getSingletonGson()
    }

    override fun <R> onConvert(succeed: Type, response: Response): R? {
        try {
            return NetConverter.onConvert<R>(succeed, response)
        } catch (e: ConvertException) {
            val code = response.code
            when {
                code in 200..299 -> { // 请求成功
                    val bodyString = response.body?.string() ?: return null
                    return gson.fromJson<R>(bodyString, succeed)
                }
                code in 400..499 -> throw RequestParamsException(response, code.toString()) // 请求参数错误
                code >= 500 -> throw ServerResponseException(response, code.toString()) // 服务器异常错误
                else -> throw ConvertException(response, message = "Http status code not within range")
            }
        }
    }
}