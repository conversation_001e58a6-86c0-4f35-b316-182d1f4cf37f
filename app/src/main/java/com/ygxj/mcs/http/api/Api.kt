package com.ygxj.mcs.http.api

import com.tencent.mmkv.MMKV
import com.ygxj.mcs.other.AppConfig

object Api {
    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("Host")
    }
    /** 四楼测试 接口地址*/
    //var HOST = "http://**************:9131"

    /** 二楼测试 接口地址*/
    //var HOST = "http://**************:8066"

    var HOST: String
        get() = mmkv.decodeString("Host","http://**************:9131").toString()
        set(value) {
            mmkv.encode("Host", value)
        }

    /**
     * VR ip
     */
    var vrIp: String?
        get() = mmkv.decodeString("vrIp")
        set(value) {
            mmkv.encode("vrIp", value)
        }

    /**
     * 接口描述:登录
     * 请求方式:POST
     * 请求参数
     * @param name
     * @param pwd
     */
    const val POST_LOGIN = "/Interface/Signin"

    /**
     * 接口描述:注册
     * 请求方式:POST
     * 请求参数
     * @param name
     * @param pwd
     */
    const val POST_SIGN_UP = "/Interface/Add"

    /**
     * 接口描述:获取量表列表
     * 请求方式:POST
     */
    const val POST_SCALE_LIST = "/Interface/Scal"

    /**
     * 接口描述:获取量表详情
     * 请求方式:POST
     * 请求参数
     * @param Id
     */
    const val POST_SCALE_DETAIL = "/Interface/ScalTable"

    /**
     * 接口描述:提交量表
     * 请求方式:POST
     * 请求参数
     * @param Id
     */
    const val POST_COMMIT_SCALE = "/Interface/ScaleLocal"

    /**
     * 接口描述:修改用户信息
     * 请求方式:POST
     * 请求参数
     * @param ID
     * @param Age
     * @param Sex
     * @param birthdayTime
     * @param Telephone
     * @param Eml
     * @param address
     * @param department
     */
    const val POST_UPDATE_USER_INFO = "/Interface/Update"

    /**
     * 接口描述:修改用户密码
     * 请求方式:POST
     * 请求参数
     * @param Id
     * @param Paw
     * @param NewPaw
     */
    const val POST_UPDATE_PWD = "/Interface/UpdatePwd"

    /**
     * 接口描述:音乐列表
     * 请求方式:POST
     */
    const val POST_MUSIC_LIST = "/Interface/music"

    /**
     * 接口描述:图片列表
     * 请求方式:POST
     */
    const val POST_IMAGE_LIST = "/Interface/images"

    /**
     * 接口描述:VR场景列表
     * 请求方式:POST
     */
    const val POST_VR_LIST = "/Interface/vr"

    /**
     * 接口描述:获取训练记录
     * 请求方式:POST
     * 请求参数
     * @param name 用户名
     */
    const val POST_TRAIN_DATA_LIST = "/Interface/Manages"

    /**
     * 接口描述:整个房间的用户数据
     * 请求方式:POST
     * 请求参数
     * @param roomid 房间id
     */
    const val POST_ROOM_INFO = "/Interface/RoomAllUsers"

    /**
     * 接口描述:获取训练列表
     * 请求方式:POST
     * 请求参数
     */
    const val POST_TRAIN_LIST = "/Interface/TrainResult"

    /**
     * 接口描述:请求是否需要在训练前填写量表
     * 请求方式:POST
     * 请求参数
     * @param id 用户id
     */
    const val POST_SCALE_NECESSARY = "/Interface/boolScale"

    /**
     * 接口描述:训练开始前的请求
     * 请求方式:POST
     * 请求参数
     * @param uid 用户id
     */
    const val POST_REQUEST_BEFORE_TRAIN = "/Interface/realTimeData"

    /**
     * 接口描述:训练结束后的请求
     * 请求方式:POST
     * 请求参数
     * @param uid 用户id
     */
    const val POST_REQUEST_AFTER_TRAIN = "/Interface/realTimeDataDel"

    /**
     * 接口描述:训练结束后的请求
     * 请求方式:POST
     * 请求参数
     * @param TrainResultEntity 训练参数
     */
    const val POST_COMMIT_TRAIN_RESULT = "/Interface/updateManage"

    /**
     * 接口描述:训练结束后的请求
     * 请求方式:POST
     * 请求参数
     * @param id 用户id
     */
    const val POST_COMMIT_ADD_TRAIN_COUNT = "/Interface/scaleCount"

    /**
     * 接口描述:实时上传数据
     * 请求方式:POST
     * 请求参数
     */
    const val POST_UPLOAD_REAL_TIME = "/Interface/RealTimeDataUpdate"

    /**
     * 接口描述:获取房间内用户状态
     * 请求方式:POST
     * 请求参数
     * @param roomid 房间id
     */
    const val POST_ROOM_USERS_STATE = "/Interface/RoomUsersZt"

    /**
     * 接口描述:获取所有用户
     * 请求方式:POST
     * 请求参数
     * @param uid 用户id
     */
    const val POST_GET_ONLINE_USERS = "/Interface/AllZxUsers"

    /**
     * 接口描述:保持用户在线状态
     * 请求方式:POST
     * 请求参数
     * @param id 用户id
     */
    const val POST_SET_ONLINE_STATE = "/Interface/LoginZT"

    /**
     * 接口描述:获取邀请
     * 请求方式:POST
     * 请求参数
     * @param uid 用户id
     */
    const val POST_GET_INVITE = "/Interface/UserSelect"

    /**
     * 接口描述:邀请用户
     * 请求方式:POST
     * 请求参数
     * @param id 用户id
     * @param yq 0表示取消邀请，1表示邀请
     */
    const val POST_INVITE_USER = "/Interface/YQUsers"

    /**
     * 接口描述:创建房间
     * 请求方式:POST
     * 请求参数
     */
    const val POST_CREATE_ROOM = "/Interface/TtxlAdd"

    /**
     * 接口描述:团体训练准备
     * 请求方式:POST
     * 请求参数
     * @param id 用户id
     * @param zbzt ,0->准备,1->不准备
     */
    const val POST_SET_READY_STATE = "/Interface/ZBZTUsers"


}