package com.ygxj.mcs.http.other

import android.app.Application
import android.content.Intent
import com.drake.net.exception.ResponseException
import com.drake.net.interfaces.NetErrorHandler
import com.hjq.toast.Toaster
import com.ygxj.mcs.other.ActivityManager
import com.ygxj.mcs.ui.login.LoginActivity


/**
 * 全局错误处理
 * token失效等 可以在此处理
 */
class GlobalErrorHandler: NetErrorHandler {

    override fun onError(e: Throwable) {
        //  数据响应异常
        if (e is ResponseException) {
            Toaster.show(e.message?:"数据异常")
            if (e.tag == "503"){
                //判断异常为token失效
                Toaster.show("请重新登录")
                // 登录信息失效，跳转到登录页
                val application: Application = ActivityManager.getInstance().getApplication()
                val intent = Intent(application, LoginActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                application.startActivity(intent)
                // 销毁除了登录页之外的 Activity
                ActivityManager.getInstance().finishAllActivities(LoginActivity::class.java)
            }
        }else{
            super.onError(e)
        }
    }
}