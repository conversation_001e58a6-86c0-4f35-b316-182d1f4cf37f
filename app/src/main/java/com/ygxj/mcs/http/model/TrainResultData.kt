package com.ygxj.mcs.http.model

import java.io.Serializable

data class TrainResultData(
    val AvgSpeed: Float = 0f, // 31
    val Calorie: Float = 0f, // 25
    val EndTime: String = "", // 2019-11-14-14:27:21
    val HeartRateList: String = "", // 24,24,58,65,85,9,54,84,51,51,58,91,101,125,10,111
    val ID: Int = 0, // 3
    val Mileage: Float = 0f, // 21
    val OxygenConsumptionList: String = "", // 24,24,58,65,85,9,54,84,51,51,58,91,101,125,10,111
    val Result: String = "", // 第一名
    val RoomID: Int = 0, // 9
    val RoomName: String = "", // 团体训练测试
    val SpeedList: String = "", // 24,24,58,65,85,9,54,84,51,51,58,91,101,125,10,111
    val HxList: String = "", // 24,24,58,65,85,9,54,84,51,51,58,91,101,125,10,111
    val StartTime: String = "", // 2019-11-14-14:27:21
    val TrainScene: String = "", // 山河瀑布
    val TrainType: String = "", // 团体训练
    val UserName: String = "", // admin
    val UsersCount: Int = 0, // 2
    val UsersName: String = "", // 用户01,admin
    val ResultPingjia: String = ""//  评价字段
) :Serializable{
}