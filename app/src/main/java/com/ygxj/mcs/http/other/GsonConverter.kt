package com.ygxj.mcs.http.other

import com.drake.net.convert.JSONConvert
import com.hjq.gson.factory.GsonFactory
import com.ygxj.mcs.http.model.BaseData
import org.json.JSONObject
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

/**
 * 正常情况下的数据解析器
 * 因为后台返回的数据格式五花八门，所以又写了GsonConverter2 和GsonConverter3
 */
class GsonConverter : JSONConvert(success = "100", code = "status", message = "message") {
    companion object {
        private val gson = GsonFactory.getSingletonGson()
    }

    override fun <R> String.parseBody(succeed: Type): R? {
        return if (isBaseDataType(succeed)) {
            gson.fromJson<R>(this, succeed)
        } else {
            val string = try {
                JSONObject(this).getString("result")
            } catch (e: Exception) {
                this
            }
            gson.fromJson<R>(string, succeed)
        }
    }

    /**
     * 判断succeed是不是BaseData类型，如果是就解析全部数据，如果不是就解析data中的数据
     */
    private fun isBaseDataType(type: Type): Bo<PERSON>an {
        if (type is ParameterizedType) {
            val rawType = type.rawType

            if (rawType is Class<*>) {
                if (BaseData::class.java.isAssignableFrom(rawType)) {
                    return true
                }
            }
        }

        return false
    }
}