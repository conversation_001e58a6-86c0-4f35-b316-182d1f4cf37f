package com.ygxj.mcs.http.other

import com.drake.net.Post
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.TrainListData
import kotlinx.coroutines.coroutineScope


/**
 * 常用的请求方法建议写到一个工具类中
 */
object HttpUtils {
    /**
     * 获取训练列表
     * 阻塞返回可直接返回结果
     *
     * @param trainType 训练类型
     */
    suspend fun getTrainList(trainType: Int = -1) = coroutineScope {
        Post<List<TrainListData>>(Api.POST_TRAIN_LIST).await()
    }

}