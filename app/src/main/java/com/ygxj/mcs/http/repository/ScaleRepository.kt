package com.ygxj.mcs.http.repository

import com.ygxj.mcs.http.model.ScaleHistoryData
import com.ygxj.mcs.other.UserManager
import org.litepal.LitePal

object ScaleRepository {

    /** 增加一条记录 **/
    fun addScaleHistory(scale: ScaleHistoryData) = scale.save()

    /** 删除单个记录 **/
    fun deleteScaleHistory(scale: ScaleHistoryData) = scale.delete()

    /**量表答题记录**/
    fun getScaleHistoryById(scaleId:String):ScaleHistoryData? = LitePal.where("userid = ? and scaleid = ?", UserManager.id.toString(),scaleId).findLast(ScaleHistoryData::class.java)


}