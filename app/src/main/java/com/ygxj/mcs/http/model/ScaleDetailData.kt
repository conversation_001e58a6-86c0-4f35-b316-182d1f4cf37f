package com.ygxj.mcs.http.model

import androidx.databinding.BaseObservable
import org.litepal.crud.LitePalSupport


/**
 * 量表列表
 */
data class ScaleDetailData(
    val Answer1: String = "", // 无
    val Answer2: String = "", // 轻度，无多大烦扰
    val Answer3: String = "", // 中度，感到不适但尚能忍受
    val Answer4: String = "", // 重度，只能勉强忍受
    val Answer5: String = "", // 0
    val Answer6: String = "", // 0
    val Answer7: String = "", // 0
    val Answer8: String = "", // 0
    val Answer9: String = "", // 0
    val Answer10: String = "", // 0
    val Answer11: String = "", // 0
    val Topic: String = "", // 21. 出汗（不是因暑热冒汗）
    val TopicID: Int = 0, // 1379
    val TopicNumber: Int = 0, // 21
    val TypeId: Int = 0// 0

)

/**
 * 选项
 */
data class OptionsData(
    /**选项内容*/
    var option: String = "",
    /**选项状态*/
    var checked: Boolean = false,
) : BaseObservable()

/**
 * 量表答题记录
 */
data class ScaleHistoryData(
    var scaleId: Int = 0,
    var userId: Int = 0,
    var answer: String = "",
    ) : LitePalSupport()