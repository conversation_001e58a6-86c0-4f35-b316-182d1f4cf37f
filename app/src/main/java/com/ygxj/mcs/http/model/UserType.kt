package com.ygxj.mcs.http.model

/**
 * 用户类型
 */
enum class UserType(val value: Int) {

    NORMAL(1) {
        override fun getTypeName(): String = "普通账号"
    },

    CONSULTANT(2) {
        override fun getTypeName(): String = "咨询师账号"
    },

    ENTERPRISE(3) {
        override fun getTypeName(): String = "企业账号"
    };

    abstract fun getTypeName(): String

    companion object {
        //根据value 返回对应的typeName
        fun getTypeName(value: Int?): String {
            for (v in UserType.values()) {
                if (value == v.value) {
                    return v.getTypeName()
                }
            }
            return ""
        }
    }

}