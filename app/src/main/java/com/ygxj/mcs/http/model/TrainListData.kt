package com.ygxj.mcs.http.model

import androidx.databinding.BaseObservable
import java.io.Serializable

/**
 * 训练任务列表
 */
data class TrainListData(
    val Display: Int = 0, // 999
    val ID: Int = 0, // 35
    var TrainDuration: String = "", // 15分钟
    val TrainName: String = "", // 身心训练3
    val TrainNotice: String = "", // 这是训练说明文本
    val TrainScene: String = "",
    val TrainTips: String = "", // 暂无提示
    val TrainType: Int = 0, // 0:基础训练 1:加强训练 2:身心训练 3:团体训练
    val TrainUsers: String = "", // 98
    val TrainZhiBiao: String = "", // 1|2
    var checked: Boolean = false,
    var Frequency: Int = 0, // 训练频率
) : BaseObservable(),Serializable {
}