package com.ygxj.mcs.utils

import android.annotation.SuppressLint
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Period
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

object DateUtil {

    /**
     * 把年月日转成 yyyy-MM-dd 格式
     */
    fun formatDate(year: Int, month: Int, day: Int): String {
        return String.format("%04d-%02d-%02d", year, month, day)
    }

    /**
     * 将date转成yyyy-MM-dd格式的字符串
     */
    fun formatDateToDateString(date: Date): String {
        val format = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return format.format(date)
    }


    /**
     * 获取当前时间
     */
    @SuppressLint("SimpleDateFormat")
    fun getCurrentDateTime(): String {
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        return dateFormat.format(calendar.time)
    }

    /**
     * 获取当前日期
     */
    @SuppressLint("SimpleDateFormat")
    fun getCurrentDate(): String {
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        return dateFormat.format(calendar.time)
    }

    /**
     * 获取两个时期之间相差多少分 多少秒
     */
    @SuppressLint("SimpleDateFormat")
    fun getSecondsBetween(dateTime1: String, dateTime2: String): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

        val parsedDateTime1 = dateFormat.parse(dateTime1)
        val parsedDateTime2 = dateFormat.parse(dateTime2)

        val milliseconds1 = parsedDateTime1.time
        val milliseconds2 = parsedDateTime2.time

        val seconds = (milliseconds2 - milliseconds1) / 1000
        val min = seconds / 60
        val sec = seconds % 60
        return String.format("%02d分%02d秒", min, sec)

    }

    /**
     * 获取一个时间到当前时间的间隔
     * 小于一天的 输出“今天”
     * 小于一个月的 输出几天
     * 小于12个月的 输出几个月
     * 大于12个月的 输出几年
     */
    fun getRelativeTime(dateStr: String?): String {
        if (dateStr.isNullOrBlank()) return ""

        val nowMillis = System.currentTimeMillis()
        val dateMillis = parseDateString(dateStr)?.time ?: return ""

        val durationMillis = nowMillis - dateMillis

        val days = TimeUnit.MILLISECONDS.toDays(durationMillis).toInt()
        val months = days / 30
        val years = days / 365

        return when {
            days <= 0 -> "今天"
            months <= 0 -> "${days}天"
            years <= 0 -> "${months}个月"
            else -> "${years}年"
        }
    }

    private fun parseDateString(dateStr: String?): Date? {
        val format = SimpleDateFormat("yyyy-MM-dd")
        return try {
            format.parse(dateStr)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 转换日期
     * 将[stringDate]按照[pattern]的格式转换
     * 例如,stringDate=2022-12-13 16:36:04,pattern=yyyy-MM-dd,则返回2022-12-13
     */
    fun changeStringDate(stringDate: String?, pattern: String): String? {
        stringDate ?: return null
        val date = stringToDate(stringDate, pattern) ?: return null
        return dateToString(date.time, pattern)
    }

    /**
     * 字符串转date
     */
    fun stringToDate(stringDate: String?, pattern: String): Date? {
        stringDate ?: return null
        return try {
            SimpleDateFormat(pattern, Locale.CHINA).parse(stringDate)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * date转字符串
     */
    fun dateToString(dateTime: Long?, pattern: String): String? {
        dateTime ?: return null
        return SimpleDateFormat(pattern, Locale.CHINESE).format(Date(dateTime))
    }

    /**
     * 计算年龄
     */
    fun calculateAge(birthdayStr: String): Int {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val birthDate = LocalDate.parse(birthdayStr, formatter)
        val currentDate = LocalDate.now()
        val period = Period.between(birthDate, currentDate)
        return period.years+1
    }
}