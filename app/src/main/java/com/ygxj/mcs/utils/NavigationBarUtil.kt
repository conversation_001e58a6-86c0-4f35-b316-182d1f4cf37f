package com.ygxj.mcs.utils

import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowManager

object NavigationBarUtil {
    /**
     * 隐藏虚拟栏 ，显示的时候再隐藏掉
     *
     * @param window
     */
    fun hideNavigationBar(window: Window?) {
        window?.decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
        window?.decorView?.setOnSystemUiVisibilityChangeListener { visibility: Int ->
            var uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or  //布局位于状态栏下方
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or  //全屏
                    View.SYSTEM_UI_FLAG_FULLSCREEN or  //隐藏导航栏
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            uiOptions = if (Build.VERSION.SDK_INT >= 19) {
                uiOptions or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            } else {
                uiOptions or View.SYSTEM_UI_FLAG_LOW_PROFILE
            }
            window.decorView.systemUiVisibility = uiOptions
        }
    }

    /**
     * dialog 需要全屏的时候用，和clearFocusNotAle() 成对出现
     * 在show 前调用  focusNotAle   show后调用clearFocusNotAle
     *
     * @param window
     */
    fun focusNotAle(window: Window?) {
        window?.setFlags(
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        )
    }

    /**
     * dialog 需要全屏的时候用，focusNotAle() 成对出现
     * 在show 前调用  focusNotAle   show后调用clearFocusNotAle
     *
     * @param window
     */
    fun clearFocusNotAle(window: Window?) {
        window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
    }
}