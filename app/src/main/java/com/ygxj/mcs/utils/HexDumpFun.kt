package com.ygxj.mcs.utils


fun Byte.toUnInt(): Int {
    return this and 0xff
}

infix fun Byte.and(mask: Int): Int = toInt() and mask

class Hex {
    companion object {
        fun bytes2hexStr(b: ByteArray): String? {
            var stmp = ""
            val sb = StringBuilder("")
            for (i in b.indices) {
                stmp = Integer.toHexString(b[i] and 0xFF)
                sb.append(if (stmp.length == 1) "0$stmp" else stmp)
                sb.append(" ") //每个Byte值之间空格分隔
            }
            return sb.toString().toUpperCase().trim { it <= ' ' }
        }

        fun hexStr2bytes(hex: String): ByteArray {
            return hex.chunked(2)
                .map { it.toByte(16) }
                .toByteArray()
        }
    }
}
