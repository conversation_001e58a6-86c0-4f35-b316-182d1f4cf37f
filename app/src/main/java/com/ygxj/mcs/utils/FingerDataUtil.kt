package com.ygxj.mcs.utils

import com.tencent.mmkv.MMKV
import com.ygxj.mcs.http.model.FingerDeviceData

object FingerDataUtil {

    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("DeviceManager")
    }

    /**
     * 指脉设备名称
     */
    var fingerName: String
        get() = mmkv.decodeString("fingerName", "BerryMed").toString()
        set(value) {
            mmkv.encode("fingerName", value)
        }


    /**指脉设备mac地址*/
    var fingerMac: String?
        get() = mmkv.decodeString("fingerMac")
        set(value) {
            mmkv.encode("fingerMac", value)
        }

    /**指脉设备ServiceUUID*/
    var fingerServiceUUID = "49535343-fe7d-4ae5-8fa9-9fafd205e455"

    /**指脉设备CharacterUUID*/
    var fingerCharacterUUID = "49535343-1e4d-4bd9-ba61-23c647249616"


    private var count = 0
    fun parseFingerData(value: ByteArray): FingerDeviceData? {
        count += 1
        var heartRateWave = 0
        var heartRate = 0
        var bloodOxy = 0
        if (value.size == 6) {
            heartRateWave = BytesOrInt.bytesToInt(value[1])
            heartRate = BytesOrInt.bytesToInt(value[3])
            bloodOxy = BytesOrInt.bytesToInt(value[4])
        }
        if (value.size == 20) {
            for (i in value.indices) {
                if (BytesOrInt.bytesToInt(value[i]) > 127) { //高位开始标识
                    heartRateWave = BytesOrInt.bytesToInt(value[i + 1])
                    heartRate = BytesOrInt.bytesToInt(value[i + 3])
                    if (BytesOrInt.bytesToInt(value[i + 2]) > 63) heartRate += 127
                    bloodOxy = BytesOrInt.bytesToInt(value[i + 4])
                    break
                }
            }
        }
        // 过滤无效值:
        // 心率: =127 或 <50 视为无效
        // 血氧: =127 或 <70 或 >100 视为无效
        if (heartRate > 50 && heartRate != 127 && bloodOxy > 70 && bloodOxy != 127 && bloodOxy <= 100) {
            if (count >= 100) {
                count = 0
                val finger = FingerDeviceData()
                finger.heartRateWave = heartRateWave
                finger.bloodOxygen = bloodOxy
                finger.heartRate = heartRate
                return finger
            }
        }
        return null
    }

    /**
     * 清除信息
     */
    fun clearDeviceData() {
        mmkv.clearAll()
    }

    /**
     * 重置参数
     */
    fun reset() {
        count = 0
    }
}