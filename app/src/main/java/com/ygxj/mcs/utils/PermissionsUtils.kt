package com.ygxj.mcs.utils

import android.content.Context
import android.os.Build
import com.bhm.ble.BleManager
import com.bhm.ble.log.BleLogger
import com.bhm.ble.utils.BleUtil
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.Toaster

object PermissionsUtils {

    // 权限数组
    private val permissionArray = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
        arrayOf(
            Permission.ACCESS_FINE_LOCATION,
            Permission.ACCESS_COARSE_LOCATION,
        )
    } else {
        arrayOf(
            Permission.BLUETOOTH_SCAN,
            Permission.BLUETOOTH_CONNECT,
        )
    }

    /**
     * 申请蓝牙权限
     */
    fun requestBlePermission(context: Context, callback: () -> Unit) {

        val isBleSupport = BleManager.get().isBleSupport()
        BleLogger.e("设备是否支持蓝牙: $isBleSupport")
        if (!isBleSupport) {
            Toaster.show("当前设备不支持蓝牙")
            return
        }

        if (!BleUtil.isGpsOpen(context)) {
            Toaster.show("请打开设备的GPS定位")
            return
        }

        if (!BleManager.get().isBleEnable()) {
            Toaster.show("请打开设备的蓝牙开关")
            return
        }

        //如果有权限
        if (XXPermissions.isGranted(context, permissionArray)) {
            callback()
            return
        }

        XXPermissions.with(context)
            .permission(permissionArray)
            .request(object : OnPermissionCallback {

                override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                    if (!allGranted) {
                        Toaster.show("获取部分权限成功，但部分权限未正常授予")
                        return
                    }
                    callback()
                }

                override fun onDenied(
                    permissions: MutableList<String>,
                    doNotAskAgain: Boolean
                ) {
                    if (doNotAskAgain) {
                        Toaster.show("被永久拒绝授权，请手动授予权限")
                        // 如果是被永久拒绝就跳转到应用权限系统设置页面
                        XXPermissions.startPermissionActivity(context, permissions)
                    } else {
                        Toaster.show("获取权限失败")
                    }
                }
            })
    }

    /**
     * 申请悬浮窗权限
     */
    fun requestAlertWindowPermission(context: Context, callback: () -> Unit) {
        if (XXPermissions.isGranted(
                context,
                Permission.SYSTEM_ALERT_WINDOW
            )
        ) {
            callback()
        } else {
            DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
                context, "提示", "为了软件功能正常使用，需要您授予悬浮窗权限"
            ) { dialog, _ ->
                dialog.dismiss()
                XXPermissions.with(context)
                    .permission(Permission.SYSTEM_ALERT_WINDOW)
                    .request(object : OnPermissionCallback {

                        override fun onGranted(
                            permissions: MutableList<String>,
                            allGranted: Boolean
                        ) {
                            if (allGranted) {
                                callback()
                            } else {
                                Toaster.show("获取部分权限成功，但部分权限未正常授予")
                            }
                        }

                        override fun onDenied(
                            permissions: MutableList<String>,
                            doNotAskAgain: Boolean
                        ) {
                            if (doNotAskAgain) {
                                Toaster.show("被永久拒绝授权，请手动授予悬浮窗权限")
                            } else {
                                Toaster.show("获取悬浮窗权限失败，请手动授予悬浮窗权限")
                            }
                        }
                    })
            }

        }
    }

}