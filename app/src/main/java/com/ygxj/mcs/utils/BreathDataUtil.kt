package com.ygxj.mcs.utils

import com.tencent.mmkv.MMKV
import com.ygxj.mcs.http.model.BreathDeviceData

object BreathDataUtil {
    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("DeviceManager")
    }

    /**呼吸设备名称*/
    var breathName : String
        get() = mmkv.decodeString("breathName","YGXJ").toString()
        set(value) {
            mmkv.encode("breathName", value)
        }

    /**呼吸mac地址*/
    var breathMac: String?
        get() = mmkv.decodeString("breathMac")
        set(value) {
            mmkv.encode("breathMac", value)
        }
    /**
     * 呼吸设备ServiceUUID
     */
    var breathServiceUUID = "00006532-0000-1000-8000-00805f9b34fb"

    /**
     * 呼吸设备CharacterUUID
     */
    var breathCharacterUUID = "00000438-0000-1000-8000-00805f9b34fb"

    /** 连续爬升次数*/
    private var upValue = 0

    /** 连续下降次数*/
    private var downValue = 0

    /** 当前峰值*/
    private var maxValue = 0

    /** 当前谷值*/
    private var minValue = 0

    /** 2个峰值间的数据位间隔*/
    private var valueCount = 0

    /** 峰值出现几次*/
    private var maxUpCount = 0

    /** 谷值出现几次*/
    private var downCount = 0

    var list = mutableListOf<Byte>()

    fun parseBreathData(by: ByteArray?): BreathDeviceData? {

        if (by == null) return null
//        Log.e("TTTTTT", "${Hex.bytes2hexStr(list.toByteArray())}")
        //首先将每次收到的数据存储集合
        by.forEach { list.add(it) }
        //如果当前存的还不到60字节,就不做处理
        if (list.size < 60) return null
        //当存够了60字节,且有0xAA(170)作为数据开始的标识
        if (list[0].toUnInt() != 170) return null
        val breathDeviceData = BreathDeviceData()
        for (i in 12 until list.size - 1) {
            valueCount++
            if (list[i].toUnInt() != 255) {
                if (list[i].toUnInt() > maxValue) {
                    //上升时中断下降计次,+1上升计次
                    downValue = 0
                    upValue++
                    //数据位连续爬升3才视为有效
                    if (upValue > 22) {
                        maxValue = list[i].toUnInt()
                        minValue = 255
                        downCount++
                        if (maxUpCount >= 2) {
                            var value = ((1000 / (valueCount * 10.0) * 60.0) - 6).toInt()
//                            Log.e("GG", "data${value}")
                            if (value > 50) value = 50
                            valueCount = 0
                            maxUpCount = 0
                            breathDeviceData.freq = value
                        }
                    }
                } else if (list[i].toUnInt() < minValue) {
                    //下降时中断上升计次,+1下降计次
                    upValue = 0
                    downValue++
                    //数据位连续下降3才视为有效
                    if (downValue > 22) {
                        minValue = list[i].toUnInt()
                        maxValue = 0
                        if (downCount > 0) {
                            maxUpCount++
                            downCount = 0
                        }
                    }
                }
            }
        }
        // 使用完之后清空
        list.clear()
        return if (breathDeviceData.freq > 0) {
            breathDeviceData
        } else {
            null
        }
    }


    /**
     * 重置参数
     */
    fun reset() {
        upValue = 0
        downValue = 0
        maxValue = 0
        minValue = 0
        valueCount = 0
        maxUpCount = 0
        downCount = 0
        list.clear()
    }
}