package com.ygxj.mcs.utils

import com.tencent.mmkv.MMKV
import com.ygxj.mcs.BuildConfig
import com.ygxj.mcs.http.model.BikeDeviceData
import timber.log.Timber

object BikeDataUtil {
    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("DeviceManager")
    }

    /**单车设备名称*/
    var bikeName: String
        get() = mmkv.decodeString("bikeName", "Sport").toString()
        set(value) {
            mmkv.encode("bikeName", value)
        }

    /**单车设备mac地址*/
    var bikeMac: String?
        get() = mmkv.decodeString("bikeMac")
        set(value) {
            mmkv.encode("bikeMac", value)
        }

    /**单车设备ServiceUUID*/
    var bikeServiceUUID = "0000fff0-0000-1000-8000-00805f9b34fb"

    /**单车设备CharacterUUID*/
    var bikeCharacterUUID = "0000fff1-0000-1000-8000-00805f9b34fb"

    /**单车设备WriteUUID*/
    var bikeWriteUUID = "0000fff2-0000-1000-8000-00805f9b34fb"

    // 切换协议命令
    val transformProtocolCMD = "15550455"

    // 数据清零命令
    val resetDataCMD = "15550355"

    /**
     * 解析单车数据
     * 数据说明示例：
     * AA 14 00 12 12 00 25 00 04 00 40 00 01 00 00 00 0A 00 98 55
     * AA 14 00 15 00 00 24 00 00 00 00 00 01 00 01 00 0A 00 45 55
     *| 0| 1| 2| 3| 4| 5| 6| 7| 8| 9|10|11|12|13|14|15|16|17|18|19|
     * 0: AA 帧头
     * 1: 14 默认值20
     * 2: 圈数高位
     * 3: 圈数低位
     * 4: 踏频(一分钟骑行圈数)
     * 5: 运动时间高位,单位秒
     * 6: 运动时间低位,单位秒
     * 7: 功率高位
     * 8: 功率低位
     * 9: 速度高位,原始速度x10,解析时需要除以10
     * 10: 速度低位,原始速度x10,解析时需要除以10
     * 11: 心率值,没有心率设备时无值
     * 12: 当前阻力值
     * 13: 卡路里高位
     * 14: 卡路里低位
     * 15: 里程高位,单位十米,如运动20.1公里，则取2010
     * 16: 里程低位
     * 17: 未使用
     * 18: sum校验,data[2]-data[17]之和的低8位
     * 19: 固定值
     */
    fun parseBikeData(value: ByteArray): BikeDeviceData {
        Timber.e("value: ${Hex.bytes2hexStr(value)}")
        // 速度,单位km/h
        val speedRaw = ((value[9].toInt() and 0xFF) shl 8) or (value[10].toInt() and 0xFF)
        val speed = speedRaw / 10.0

        // 里程,单位km
        val distanceRaw = ((value[15].toInt() and 0xFF) shl 8) or (value[16].toInt() and 0xFF)
        // 设备给的里程单位是10m,计算km方式为x10/1000,也就是/100
        val distance = distanceRaw / 100.0

        // 卡路里,单位kcal
        val caloriesRaw = ((value[13].toInt() and 0xFF) shl 8) or (value[14].toInt() and 0xFF)
        val calories = caloriesRaw / 10.0

        // 耗氧量
        val vo2 = String.format("%.2f", (22.351 - 11.288) * distance).toDouble()

        if (BuildConfig.DEBUG) {
            val tmpSpeed = "速度:${value[9].toString(16)}${value[10].toString(16)}->$speed"
            val tmpCalories = "卡路里:${value[13].toString(16)}${value[14].toString(16)}->$calories"
            val tmpDistance = "里程:${value[15].toString(16)}${value[16].toString(16)}->$distance"
            val tmpVo2 = "耗氧量:${vo2}"
            Timber.e("$tmpSpeed $tmpCalories $tmpDistance $tmpVo2")
        }
        return BikeDeviceData(speed, distance, calories, vo2)
    }


    /**
     * 重置所有参数
     */
    fun reset() {

    }
}