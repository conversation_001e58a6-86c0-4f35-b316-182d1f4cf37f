package com.ygxj.mcs.utils

import android.content.Context
import com.luck.picture.lib.engine.UriToFileTransformEngine
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener
import com.luck.picture.lib.utils.SandboxTransformUtils


/**
 * 自定义沙盒文件处理 解决安卓10以上获取不到真实文件路径问题
 */
class AudioSandboxFileEngine : UriToFileTransformEngine {
    override fun onUriToFileAsyncTransform(
        context: Context,
        srcPath: String,
        mineType: String,
        call: OnKeyValueResultCallbackListener
    ) {
        call.onCallback(
            srcPath,
            SandboxTransformUtils.copyPathToSandbox(context, srcPath, "audio/mp3")
        )
    }
}
