package com.ygxj.mcs.utils

import android.os.CountDownTimer

/**
 * 自定义倒计时器,实现计时/暂停/恢复功能
 * @param millisTotal 总时间,毫秒值
 * @param countDownInterval 倒计时间隔,毫秒值
 * @param listener 计时回调
 */
class CustomCountDownTimer(millisTotal: Long, val countDownInterval: Long, val listener: TimerListener) {
    private var timer: CountDownTimer? = null
    private var timeRecorder: Long = millisTotal
    /**
     * 开始计时
     */
    fun start() {
        timer = object : CountDownTimer(timeRecorder, countDownInterval) {
            override fun onFinish() {
                listener.onFinish()
            }

            override fun onTick(millisUntilFinished: Long) {
                timeRecorder = millisUntilFinished
                listener.onTick(millisUntilFinished)
            }
        }.start()
    }

    /**
     * 暂停
     */
    fun pause() {
        timer?.cancel()
    }

    /**
     * 恢复之前的计时
     */
    fun goOn() {
        start()
    }
}

interface TimerListener {
    /**
     * 时间计数发送变化
     * @param millisRemain 剩余时间,毫秒值
     */
    fun onTick(millisRemain: Long)

    /**计时完成*/
    fun onFinish()

}


