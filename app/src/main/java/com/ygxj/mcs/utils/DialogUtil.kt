package com.ygxj.mcs.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import com.qmuiteam.qmui.widget.dialog.QMUIDialog
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


object DialogUtil {

    /**
     * 使用[text]创建一个加载弹窗,显示后将对象返回
     */
    fun showLoadingDialog(context: Context, text: String): QMUITipDialog {
        val loadingDialog = createDialog(context, text, QMUITipDialog.Builder.ICON_TYPE_LOADING)
        loadingDialog.apply {
            setCanceledOnTouchOutside(false)
            setCancelable(false)
            show()
        }
        return loadingDialog

    }

    /**
     * 隐藏[dialog]
     */
    fun hideLoadingDialog(dialog: QMUITipDialog?) {
        dialog?.let {
            if (it.isShowing) it.dismiss()
        }
    }

    /**
     * 显示一个文本内容为[text]的提示框,并在1.5s后自动隐藏
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun showTipDialog(context: Context, text: String) {
        val dialog = createDialog(context, text, QMUITipDialog.Builder.ICON_TYPE_NOTHING)
        dialog.show()
        GlobalScope.launch {
            delay(1500)
            dialog.dismiss()
        }
    }

    /**
     * 显示一个文本内容为[text]的错误框,并在1.5s后隐藏
     */
    @OptIn(DelicateCoroutinesApi::class)
    @SuppressLint("CheckResult")
    fun showAutoDismissErrorDialog(context: Context, text: String) {
        val dialog = createDialog(context, text, QMUITipDialog.Builder.ICON_TYPE_FAIL)
        dialog.show()
        GlobalScope.launch {
            delay(1500)
            dialog.dismiss()
        }
    }

    /**
     * 显示一个文本内容为[text]的错误对话框,在1.5s后隐藏,并提供隐藏回调[listener]
     */
    @OptIn(DelicateCoroutinesApi::class)
    @SuppressLint("CheckResult")
    fun showAutoDismissErrorDialogWithListener(
        context: Context,
        text: String,
        listener: DialogInterface.OnDismissListener
    ) {
        val dialog = createDialog(context, text, QMUITipDialog.Builder.ICON_TYPE_FAIL)
        dialog.show()
        dialog.setOnDismissListener(listener)
        GlobalScope.launch {
            delay(1500)
            dialog.dismiss()
        }
    }

    /**
     * 显示一个文本内容为[text]的成功对话框,在1.5s后隐藏
     */
    @OptIn(DelicateCoroutinesApi::class)
    @SuppressLint("CheckResult")
    fun showAutoDismissSuccessDialog(context: Context, text: String) {
        val dialog = createDialog(context, text, QMUITipDialog.Builder.ICON_TYPE_SUCCESS)
        dialog.show()
        GlobalScope.launch {
            delay(1500)
            dialog.dismiss()
        }
    }

    /**
     * 显示一个文本内容为[text]的成功对话框,在1.5s后隐藏,并提供隐藏回调[listener]
     */
    @OptIn(DelicateCoroutinesApi::class)
    @SuppressLint("CheckResult")
    fun showAutoDismissSuccessDialogWithListener(
        context: Context,
        text: String,
        listener: DialogInterface.OnDismissListener
    ) {
        val dialog = createDialog(context, text, QMUITipDialog.Builder.ICON_TYPE_SUCCESS)
        dialog.show()
        dialog.setOnDismissListener(listener)
        GlobalScope.launch {
            delay(1500)
            dialog.dismiss()
        }
    }

    /**
     * 创建一个[iconType]类型的,显示文本为[text]的对话框并返回
     */
    private fun createDialog(context: Context, text: String, iconType: Int): QMUITipDialog {
        return QMUITipDialog.Builder(context)
            .setIconType(iconType)
            .setTipWord(text)
            .create(false)
    }

    /**
     * 显示一个仅有确定按钮和确定回调的对话框
     */
    @JvmStatic
    fun showDialogWithConfirmCallBack(
        context: Context,
        title:String ,
        text: String,
        confirmListener: QMUIDialogAction.ActionListener
    ) {
        QMUIDialog.MessageDialogBuilder(context)
            .setTitle(title)
            .setMessage(text)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .addAction(0, "确定", QMUIDialogAction.ACTION_PROP_NEGATIVE, confirmListener)
            .create(com.qmuiteam.qmui.R.style.QMUI_Dialog)
            .show()
    }

    /**
     * 显示一个有取消按钮/确定按钮和确定回调的对话框
     */
    @JvmStatic
    fun showDialogWithConfirmCallBackAndCancelButton(
        context: Context,
        title: String,
        text: String,
        confirmListener: QMUIDialogAction.ActionListener
    ) {
        QMUIDialog.MessageDialogBuilder(context)
            .setTitle(title)
            .setMessage(text)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .addAction("取消") { dialog, _ ->
                dialog.dismiss()
            }
            .addAction(0, "确定", QMUIDialogAction.ACTION_PROP_NEGATIVE, confirmListener)
            .create(com.qmuiteam.qmui.R.style.QMUI_Dialog)
            .show()
    }


    /**
     * 显示一个有取消按钮/取消回调/确定按钮/确定回调的对话框
     */
    @JvmStatic
    fun showDialogWithConfirmAndCancelCallBack(
        context: Context,
        text: String,
        confirmListener: QMUIDialogAction.ActionListener,
        cancelBtnText: String? = "取消",
        cancelListener: QMUIDialogAction.ActionListener
    ) {
        QMUIDialog.MessageDialogBuilder(context)
            .setTitle("提示")
            .setMessage(text)
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .addAction(cancelBtnText, cancelListener)
            .addAction(0, "确定", QMUIDialogAction.ACTION_PROP_NEGATIVE, confirmListener)
            .create(com.qmuiteam.qmui.R.style.QMUI_Dialog)
            .show()
    }

    /**
     * 智能调整对话框
     */
    @JvmStatic
    fun showDialogWithSmartAdjustment(
        context: Context,
        cancelListener: QMUIDialogAction.ActionListener,
        confirmListener: QMUIDialogAction.ActionListener,
    ) {
        QMUIDialog.MessageDialogBuilder(context)
            .setTitle("提示")
            .setMessage("是否开启智能调整功能")
            .setCancelable(false)
            .setCanceledOnTouchOutside(false)
            .addAction("关闭", cancelListener)
            .addAction(0, "开启", QMUIDialogAction.ACTION_PROP_NEGATIVE, confirmListener)
            .create(com.qmuiteam.qmui.R.style.QMUI_Dialog)
            .show()
    }
}