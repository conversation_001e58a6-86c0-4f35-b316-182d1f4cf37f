package com.ygxj.mcs.utils

import android.media.MediaPlayer
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.ResourceData

/**
 * 音频播放器
 */
object AudioPlayer {

    /**播放器**/
    var mediaPlayer: MediaPlayer? = null

    /**是否暂停**/
    var isPaused = false

    /**播放完成监听**/
    private var completionListener: (() -> Unit)? = null

    /**准备完成监听**/
    private var preparedListener: (() -> Unit)? = null

    /**是否显示悬浮窗**/
    var isShowFloatView = false

    /**是否显示悬浮窗内播放控件**/
    var isShowFloatViewContent = false

    /**音乐列表**/
    var musicList:List<ResourceData> = listOf()

    /**当亲播放的音乐的位置**/
    var currentIndex = 0

    /**
     * 初始化MediaPlayer并设置监听器
     */
    private fun initializeMediaPlayer(url: String, loop: Boolean) {
        mediaPlayer?.release()
        mediaPlayer = MediaPlayer().apply {
            setDataSource(Api.HOST + url)
            setOnPreparedListener {
                start()
                isPaused = false
                preparedListener?.invoke()
            }
            setOnCompletionListener {
                if (loop) playNext()
                completionListener?.invoke()
            }
            prepareAsync()
        }
    }

    /**
     * 开始播放音频
     * @param url 音频文件的URL
     */
    fun startPlaying(url: String) {
        initializeMediaPlayer(url, false)
    }

    /**
     * 循环播放
     */
    fun startLoopPlaying(url: String) {
        initializeMediaPlayer(url, true)
    }


    /**
     * 暂停播放
     */
    fun pausePlaying() {
        if (mediaPlayer?.isPlaying == true) {
            mediaPlayer?.pause()
            isPaused = true
        }
    }

    /**
     * 恢复播放
     */
    fun resumePlaying() {
        if (isPaused) {
            mediaPlayer?.start()
            isPaused = false
        }
    }

    /**
     * 停止播放并释放资源
     */
    fun stopPlaying() {
        mediaPlayer?.setOnCompletionListener(null)
        mediaPlayer?.setOnPreparedListener(null)
        mediaPlayer?.stop()
        mediaPlayer?.release()
        mediaPlayer = null
    }


    /**
     * 设置播放完成监听器
     * @param listener 播放完成回调函数
     */
    fun setCompletionListener(listener: (() -> Unit)?) {
        completionListener = listener
    }

    /**
     * 设置播放器准备完成监听器
     * @param listener 准备完成回调函数
     */
    fun setPreparedListener(listener: (() -> Unit)?) {
        preparedListener = listener
    }

    /**
     * 获取当前播放进度
     */
    fun getCurrentPosition(): Int {
        return mediaPlayer?.currentPosition ?: 0
    }

    /**
     * 获取音频总时长
     */
    fun getDuration(): Int {
        return mediaPlayer?.duration ?: 0
    }

    /**
     * 跳转到指定位置
     */
    fun seekTo(position: Int) {
        mediaPlayer?.seekTo(position)
    }

    /**
     * 播放下一首音乐
     */
    private fun playNext(){
        if (musicList.isEmpty()) return

        if (currentIndex < musicList.size - 1) {
            currentIndex++
        } else {
            currentIndex = 0
        }
        stopPlaying()
        startLoopPlaying(musicList[currentIndex].MusicUrl)
    }

    /**
     * 释放资源
     */
    fun release() {
        mediaPlayer?.setOnCompletionListener(null)
        mediaPlayer?.setOnPreparedListener(null)
        completionListener = null
        preparedListener = null
        mediaPlayer?.release()
        mediaPlayer = null
    }

}