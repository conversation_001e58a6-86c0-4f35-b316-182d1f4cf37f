package com.ygxj.mcs.utils

import com.blankj.utilcode.util.ResourceUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.model.TrainSceneData
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.other.TrainType

object TrainUtil {
    /**
     * 获取本地存储的训练场景
     * Assets中存储了一个json文件,此文件与Assets中video目录下的文件对应,映射了文件名称和场景名称,方便后期维护
     * 因此,无论修改video中的文件还是修改json文件,都要保证两者对应
     */
    fun getTrainScene(): List<TrainSceneData> {
        val listType = object : TypeToken<List<TrainSceneData>>() {}.type
        val entity: List<TrainSceneData> = Gson().fromJson(ResourceUtils.readAssets2String("train_scene.json"), listType)
        val list = mutableListOf<TrainSceneData>()
        for (s in entity) {
            if(AppConfig.enableVr){
                list.add(s)
            } else {
                if(!s.isVR) list.add(s)
            }
        }
        return list
    }


    /**
     * 组织某条训练的训练说明文字
     */
    fun getTrainIntro(entity: TrainListData): String {
        val successHeartPercent = entity.TrainZhiBiao.split("|")
        if (successHeartPercent.size >= 2) {
            var intro = "训练时长：${entity.TrainDuration}\n" +
                    //"训练成功标志：达到心率最大值的${successHeartPercent[0]}%~${successHeartPercent[1]}%\n" +
                    "训练说明：${entity.TrainNotice}\n" +
                    "训练提示：${entity.TrainTips}\n" +
                    "训练频率：一周训练${entity.Frequency}次"
            if (entity.TrainType == TrainType.TEAM) {
                intro = "训练场景: ${entity.TrainScene}\n" + intro
            }
            return intro
        }
        return "暂无训练说明"
    }
}