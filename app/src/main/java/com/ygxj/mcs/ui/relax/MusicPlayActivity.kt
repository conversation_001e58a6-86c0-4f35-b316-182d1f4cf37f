package com.ygxj.mcs.ui.relax


import android.content.Context
import android.content.Intent
import android.view.Gravity
import android.view.View
import android.widget.SeekBar
import androidx.lifecycle.lifecycleScope
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.window.EasyWindow
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityMusicPlayBinding
import com.ygxj.mcs.http.model.ResourceData
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.utils.AudioPlayer
import com.ygxj.mcs.utils.PermissionsUtils
import com.ygxj.mcs.utils.TimeUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 音乐播放界面
 */
class MusicPlayActivity : BaseActivity<ActivityMusicPlayBinding>(),
    SeekBar.OnSeekBarChangeListener {

    private val mResourceData by lazy { intent.getSerializableExtra("resourceData") as ResourceData }

    private val mPosition by lazy { intent.getIntExtra("position",0) }

    companion object {
        fun start(context: Context, resourceData: ResourceData,position:Int = 0) {
            val intent = Intent(context, MusicPlayActivity::class.java)
            intent.putExtra("resourceData", resourceData)
            intent.putExtra("position", position)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_music_play

    override fun initView() {
        setOnClickListener(R.id.ivBack, R.id.ivPlay)
        EasyWindow.recycleAllWindow()
        binding.seekBar.setOnSeekBarChangeListener(this)//进度条监听
        PermissionsUtils.requestAlertWindowPermission(this){}
    }

    override fun initData() {
        binding.tvMusicName.text = mResourceData.MusicName
        // 准备完成监听
        AudioPlayer.setPreparedListener {
            updateUi()
        }
        //播放完成监听
        AudioPlayer.setCompletionListener {
            binding.tvMusicName.text = AudioPlayer.musicList[AudioPlayer.currentIndex].MusicName
        }
        playMusic(mResourceData)
    }

    /**
     * 播放音乐
     */
    private fun playMusic(resource: ResourceData) {
        AudioPlayer.currentIndex = mPosition
        AudioPlayer.stopPlaying()
        AudioPlayer.startLoopPlaying(resource.MusicUrl)

        lifecycleScope.launch {
            repeat(Int.MAX_VALUE) {
                binding.tvPlayedTime.text =
                    TimeUtil.formatMillionSeconds(AudioPlayer.getCurrentPosition().toLong())
                binding.seekBar.progress = AudioPlayer.getCurrentPosition()
                delay(1000)
            }
        }
    }

    /**
     * 音乐准备完成后更新界面
     */
    private fun updateUi(){
        binding.tvTotalTime.text =
            TimeUtil.formatMillionSeconds(AudioPlayer.getDuration().toLong())
        binding.seekBar.max = AudioPlayer.getDuration()
        binding.ivPlay.isSelected = true
        binding.tvPlayedTime.text =
            TimeUtil.formatMillionSeconds(AudioPlayer.getCurrentPosition().toLong())
        binding.seekBar.progress = AudioPlayer.getCurrentPosition()
    }

    /**
     * 点击事件
     */
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> finish()

            R.id.ivPlay -> togglePlayState()
        }
    }

    /**
     * 切换播放状态
     */
    private fun togglePlayState() {
        if (AudioPlayer.isPaused) {
            AudioPlayer.resumePlaying()
            binding.ivPlay.isSelected = false
        } else {
            AudioPlayer.pausePlaying()
            binding.ivPlay.isSelected = true
        }
    }

    /**
     * seekbar状态监听
     */
    override fun onProgressChanged(p0: SeekBar?, progress: Int, fromUser: Boolean) {
        if (fromUser) {
            binding.tvPlayedTime.text =
                TimeUtil.formatMillionSeconds(p0?.progress?.toLong() ?: 0)
            AudioPlayer.seekTo(progress)
        }
    }

    override fun onStartTrackingTouch(p0: SeekBar?) {

    }

    override fun onStopTrackingTouch(p0: SeekBar?) {

    }

    /**
     * 显示播放音乐的悬浮窗
     */
    private fun showMusicFloatView() {
        if (XXPermissions.isGranted(this, Permission.SYSTEM_ALERT_WINDOW)){
            EasyWindow.with(application)
                .setContentView(MusicView(this))
                .setDraggable(VerticalMovingDraggable())
                .setGravity(Gravity.START or Gravity.BOTTOM)
                .setYOffset(200)
                .show()
        }else{
            AudioPlayer.release()
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        showMusicFloatView()
    }


}