package com.ygxj.mcs.ui.main

import android.view.View
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentTrainBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.dialog.DialogInputIp
import com.ygxj.mcs.ui.dialog.DialogSystemTrain
import com.ygxj.mcs.ui.train.ShenXinTrainListActivity
import com.ygxj.mcs.ui.train.SystemTrainListActivity
import com.ygxj.mcs.ui.train.TeamTrainActivity
import com.ygxj.mcs.ui.train.TiXinTrainListActivity
import com.ygxj.mcs.ui.train.VRGameSceneActivity
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.visible

/**
 * 训练中心
 */
class TrainFragment : BaseFragment<MainPageActivity, FragmentTrainBinding>() {

    companion object {

        fun newInstance(): TrainFragment {
            return TrainFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_train
    }

    override fun initView() {
        setOnClickListener(
            R.id.tvConfig,
            R.id.ibSystemTrain,
            R.id.ibTiXinTrain,
            R.id.ibShenXinTrain,
            R.id.btnVRGameScene
        )

        //是否开放VR功能
        if (AppConfig.enableVr) {
            binding.llVrIpConfigView.visible()
            //加载VR设备信息
            binding.tvVrIp.text =
                if (Api.vrIp.isNullOrBlank()) "当前VR设备地址：未配置" else "当前VR设备地址：${Api.vrIp}"
        } else {
            binding.llVrIpConfigView.gone()
        }

    }

    override fun initData() {

    }

    /**
     * 点击事件
     */
    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            //配置vr地址
            R.id.tvConfig -> showConfigVrIpDialog()

            //体心训练
            R.id.ibTiXinTrain -> {
                if (UserManager.id == -1) {
                    Toaster.show("您当前是游客身份，无法进行训练！")
                    return
                }
                if (checkAgeIsEmpty()) return
                if (AppConfig.enableVr && Api.vrIp.isNullOrBlank()) {
                    showConfigVrIpDialog()
                } else {
                    startActivity(TiXinTrainListActivity::class.java)
                }
            }

            //身心训练
            R.id.ibShenXinTrain -> {
                if (UserManager.id == -1) {
                    Toaster.show("您当前是游客身份，无法进行训练！")
                    return
                }
                if (checkAgeIsEmpty()) return
                if (AppConfig.enableVr && Api.vrIp.isNullOrBlank()) {
                    showConfigVrIpDialog()
                } else {
                    startActivity(ShenXinTrainListActivity::class.java)
                }
            }

            //系统推荐训练
            R.id.ibSystemTrain -> {
                if (UserManager.id == -1) {
                    Toaster.show("您当前是游客身份，无法进行训练！")
                    return
                }
                if (checkAgeIsEmpty()) return
                if (AppConfig.enableVr && Api.vrIp.isNullOrBlank()) {
                    showConfigVrIpDialog()
                } else {
                    DialogSystemTrain(requireContext()) { isNovice, isSport ->
                        SystemTrainListActivity.start(requireContext(), isNovice, isSport)
                    }.build().show()

                }
            }
            //VR游戏场景
            R.id.btnVRGameScene -> {
                startActivity(VRGameSceneActivity::class.java)
            }
        }
    }

    /**
     * 团体训练
     */
    private fun teamTrain() {
        if (UserManager.id == -1) {
            Toaster.show("您当前是游客身份，无法进行训练！")
            return
        }
        if (checkAgeIsEmpty()) return
        if (AppConfig.enableVr && Api.vrIp.isNullOrBlank()) {
            showConfigVrIpDialog()
        } else {
            startActivity(TeamTrainActivity::class.java)
        }
    }

    /**
     * 先判断年龄是否为空，因为训练数据的计算需要年龄。
     */
    private fun checkAgeIsEmpty(): Boolean {
        if (UserManager.age.isNullOrBlank()) {
            DialogUtil.showDialogWithConfirmCallBack(
                requireContext(),
                "提示",
                "您的年龄信息未填写，无法进行训练，点击前往个人中心完善年龄信息！"
            ) { dialog, _ ->
                dialog.dismiss()
                getAttachActivity()?.switchToIndex(4)
            }
            return true
        }
        return false
    }

    /**
     * 展示帮助信息
     */
    private fun showHelpInfo() {
        val info =
            "体心训练：由自主训练、基础训练、加强训练三个模块组成，可以进行自由训练以及基本的身体训练来舒缓心理负面状态。\n" +
                    "身心训练：此模块设定了符合自身状况的专业训练方案，可以进行专业的身体和心理的训练。"
        DialogUtil.showDialogWithConfirmCallBack(
            requireContext(),
            "提示",
            info
        ) { dialog, _ ->
            dialog.dismiss()
        }
    }

    /**
     * 配置VR设备地址
     */
    private fun showConfigVrIpDialog() {
        DialogInputIp(requireContext()) { ip ->
            Api.vrIp = ip
            binding.tvVrIp.text = "当前VR设备地址：$ip"
        }.build().show()
    }

}