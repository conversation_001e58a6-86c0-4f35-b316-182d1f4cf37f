package com.ygxj.mcs.ui.train


import android.view.View
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityTeamTrainBinding
import com.ygxj.mcs.other.FragmentPagerAdapter
import com.ygxj.mcs.other.TrainType
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.ui.base.BaseFragment

/**
 * 团体训练
 */
class TeamTrainActivity : BaseActivity<ActivityTeamTrainBinding>() {

    private var pagerAdapter: FragmentPagerAdapter<BaseFragment<*, *>>? = null

    override fun getLayoutId(): Int = R.layout.activity_team_train

    override fun initView() {
        setOnClickListener(R.id.tvInvite, R.id.tvTeamMission)
        pagerAdapter = FragmentPagerAdapter<BaseFragment<*, *>>(this).apply {
            addFragment(InviteTrainFragment.newInstance())
            addFragment(CommonTrainFragment.newInstance(TrainType.TEAM))
            binding.vp.adapter = this
        }
        //禁止vp滑动
        binding.vp.setSwipeable(false)
    }

    override fun initData() {

    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when(view.id){
            R.id.tvInvite -> {
                binding.tvInvite.alpha = 1f
                binding.tvTeamMission.alpha = 0.5f
                binding.vp.setCurrentItem(0, false)
            }

            R.id.tvTeamMission -> {
                binding.tvInvite.alpha = 0.5f
                binding.tvTeamMission.alpha = 1f
                binding.vp.setCurrentItem(1, false)
            }
        }
    }
}