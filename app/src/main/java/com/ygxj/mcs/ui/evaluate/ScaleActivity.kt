package com.ygxj.mcs.ui.evaluate


import android.view.View
import androidx.core.content.ContentProviderCompat.requireContext
import com.drake.net.Post
import com.drake.net.utils.scope
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityPressureBinding
import com.ygxj.mcs.databinding.ActivityScaleBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.ScaleListData
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.utils.visible

/**
 * 量表评估
 */
class ScaleActivity : BaseActivity<ActivityScaleBinding>() {

    private var dataList: List<ScaleListData> = arrayListOf()
    override fun getLayoutId(): Int = R.layout.activity_scale

    override fun initView() {
        setOnClickListener(R.id.ibScale1, R.id.ibScale2, R.id.ibScale3)
    }

    override fun initData() {
        binding.state.onRefresh {
            scope {
                val resp = Post<List<ScaleListData>>(Api.POST_SCALE_LIST){
                    param("type",0)
                }.await()
                dataList = resp
                binding.state.showContent()
                val scaleContainers = arrayOf(binding.llScale1, binding.llScale2, binding.llScale3)
                for (i in resp.indices) {
                    scaleContainers[i].visible()
                }
            }
        }.showLoading()
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ibScale1 -> {
                ScaleDetailActivity.start(this, dataList[0].Id, "心理评估1")
            }

            R.id.ibScale2 -> {
                ScaleDetailActivity.start(this, dataList[1].Id, "心理评估2")
            }

            R.id.ibScale3 -> {
                ScaleDetailActivity.start(this, dataList[2].Id, "心理评估3")
            }
        }
    }

}