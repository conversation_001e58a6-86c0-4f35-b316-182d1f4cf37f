package com.ygxj.mcs.ui.view

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.ygxj.mcs.R
import com.ygxj.mcs.other.AppConfig

/**
 * logoView,可以根据'是否显示阳光心健logo'配置动态切换
 */
class LogoView(context: Context, attrs: AttributeSet?) : AppCompatImageView(context, attrs) {
    init {
        if (AppConfig.SHOW_YGXJ_LOGO) {
            setImageResource(R.drawable.ic_logo)
        } else {
            setImageResource(R.drawable.ic_logo_empty)
        }
    }
}