package com.ygxj.mcs.ui.login


import android.view.View
import com.drake.net.Post
import com.drake.net.utils.scopeDialog
import com.hjq.toast.Toaster
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityRegisterBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.utils.DialogUtil

/**
 * 注册页面
 */
class RegisterActivity : BaseActivity<ActivityRegisterBinding>() {

    override fun getLayoutId(): Int = R.layout.activity_register

    override fun initView() {
        setOnClickListener(R.id.btnRegister)
        binding.titleView.hideLogo()

    }

    override fun initData() {

    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnRegister -> {
                if (binding.etName.text.toString().isEmpty()) {
                    Toaster.show("请输入用户名")
                    return
                }
                if (binding.etPwd.text.toString().isEmpty()) {
                    Toaster.show("请输入密码")
                    return
                }
                if (binding.etConfirmPwd.text.toString().isEmpty()) {
                    Toaster.show("请再次输入密码")
                    return
                }
                if (binding.etPwd.text.toString() != binding.etConfirmPwd.text.toString()) {
                    Toaster.show("两次密码输入不一致")
                    return
                }
                scopeDialog {
                    Post<Any?>(Api.POST_SIGN_UP){
                        param("name",binding.etName.text.toString())
                        param("pwd",binding.etPwd.text.toString())
                    }.await()
                    DialogUtil.showAutoDismissSuccessDialogWithListener(this@RegisterActivity,"注册成功,管理员审核通过后，即可使用此账号登录"){
                        finish()
                    }
                }
            }
        }
    }


}