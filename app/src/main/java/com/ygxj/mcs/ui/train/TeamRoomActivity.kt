package com.ygxj.mcs.ui.train


import android.content.Context
import android.content.Intent
import android.os.CountDownTimer
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.bhm.ble.BleManager
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.net.Post
import com.drake.net.utils.scope
import com.drake.net.utils.scopeDialog
import com.drake.net.utils.scopeNetLife
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityTeamRoomBinding
import com.ygxj.mcs.databinding.ItemTeamRoomUserBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.RoomUsersStateData
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.model.TrainSceneData
import com.ygxj.mcs.http.other.GsonConverter2
import com.ygxj.mcs.http.other.GsonConverter3
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.visible
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 团体训练房间
 */
class TeamRoomActivity : BaseActivity<ActivityTeamRoomBinding>() {

    private val mTrainListData by lazy { intent.getSerializableExtra("trainListData") as TrainListData }//训练任务
    private val mTrainSceneData by lazy { intent.getSerializableExtra("trainSceneData") as TrainSceneData }//训练场景
    private var job: Job? = null

    companion object {
        fun start(
            context: Context,
            trainListData: TrainListData,
            trainSceneData: TrainSceneData,
        ) {
            val intent = Intent(context, TeamRoomActivity::class.java)
            intent.putExtra("trainListData", trainListData)
            intent.putExtra("trainSceneData", trainSceneData)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_team_room

    override fun initView() {
        setOnClickListener(R.id.ivBack, R.id.btnReady, R.id.btnDevice)

        binding.rv.setup {
            addType<RoomUsersStateData>(R.layout.item_team_room_user)
            onBind {
                val model = getModel<RoomUsersStateData>()
                val item = getBinding<ItemTeamRoomUserBinding>()
                if (model.ZT == "1") {
                    item.tvState.text = "未准备"
                    item.progressBar.visible()
                    item.ivReady.gone()
                } else {
                    item.tvState.text = "已准备"
                    item.progressBar.gone()
                    item.ivReady.visible()
                }
            }
        }
    }

    override fun initData() {
        binding.state.onRefresh {
            scope {
                val resp = Post<List<RoomUsersStateData>>(Api.POST_ROOM_USERS_STATE) {
                    converter = GsonConverter2()
                    param("roomid", mTrainListData.ID)
                }.await()
                if (resp.isEmpty()) {
                    showEmpty()
                } else {
                    showContent()
                    binding.rv.models = resp
                    val allReady = resp.all { it.ZT == "0" }
                    //所有人都准备好了
                    if (allReady) {
                        startCountDown()
                    }
                }
            }
        }.showLoading()

        job = lifecycleScope.launch {
            while (true) {
                delay(1000)
                binding.state.refresh()
            }
        }
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> onBackPressed()

            R.id.btnReady -> ready()

            R.id.btnDevice -> startActivity(DeviceManagerActivity::class.java)
        }
    }

    /**
     * 准备
     */
    private fun ready() {
        if (BleManager.get().getAllConnectedDevice()?.size != 3) {
            DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
                this,
                "提示",
                "当前设备未正确连接，请前往设备管理连接后再进行准备！"
            ) { dialog, _ ->
                dialog.dismiss()
                startActivity(DeviceManagerActivity::class.java)
            }
            return
        }

        scopeDialog {
            Post<Any?>(Api.POST_SET_READY_STATE) {
                converter = GsonConverter3()
                param("id", UserManager.id)
                param("zbzt", "0")
            }.await()
        }
    }

    /**
     * 开始倒计时
     */
    private fun startCountDown() {
        job?.cancel()
        binding.tvReadyTimer.text = "3"
        object : CountDownTimer(4000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                when (val remainTime = (millisUntilFinished / 1000).toInt()) {
                    0 -> binding.tvReadyTimer.text = "开始"
                    else -> binding.tvReadyTimer.text = remainTime.toString()
                }
            }

            override fun onFinish() {
                binding.tvReadyTimer.text = ""
                TrainActivity.start(this@TeamRoomActivity, mTrainListData, mTrainSceneData,false)
                finish()
            }
        }.start()
    }


    /**
     * 设置准备状态 0是准备好 1是未准备好
     */
    private fun setReadyState(ready: Boolean) {
        scopeNetLife {
            Post<Any?>(Api.POST_SET_READY_STATE) {
                converter = GsonConverter3()
                param("id", UserManager.id)
                param("zbzt", if (ready) "0" else "1")
            }.await()
        }
    }

    override fun onBackPressed() {
        DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
            this,
            "提示",
            "确定要退出房间吗？"
        ) { dialog, _ ->
            dialog.dismiss()
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        job?.cancel()
        setReadyState(false)
        super.onDestroy()
    }
}