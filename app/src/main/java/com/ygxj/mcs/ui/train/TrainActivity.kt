package com.ygxj.mcs.ui.train


import android.content.Context
import android.content.Intent
import android.view.View
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.core.graphics.toColorInt
import androidx.core.view.isGone
import androidx.lifecycle.ViewModelProvider
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityTrainBinding
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.model.TrainSceneData
import com.ygxj.mcs.other.ActivityManager
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.other.TrainType
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.PermissionsUtils
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.visible


/**
 * 训练界面
 */
class TrainActivity : BaseActivity<ActivityTrainBinding>() {

    private val mTrainListData by lazy { intent.getSerializableExtra("trainListData") as TrainListData }//训练任务
    private val mTrainSceneData by lazy { intent.getSerializableExtra("trainSceneData") as TrainSceneData }//训练场景
    private val mSmartAdjustment by lazy {
        intent.getBooleanExtra(
            "smartAdjustment",
            false
        )
    }//智能调整功能是否开启
    private var mPlayer: ExoPlayer? = null //视频播放器
    private lateinit var model: TrainViewModel    // ViewModel 实例

    private lateinit var heartLineData: LineData     // 心率图表数据源
    private val heartEntries = ArrayList<Entry>()  // 心率图表数据集合,就是一个个的点
    private val heartDataSet = LineDataSet(heartEntries, "心率") // 心率折线,就是点的封装,连城线

    private lateinit var breathLineData: LineData //呼吸图表数据源
    private val breathEntries = ArrayList<Entry>() // 呼吸图表数据集合,就是一个个点
    private val breathDataSet = LineDataSet(breathEntries, "呼吸") // 呼吸折线图,就是点的封装,连成线

    private var trainStarted = false  //训练是否已经开始了

    companion object {
        fun start(
            context: Context,
            trainListData: TrainListData,
            trainSceneData: TrainSceneData,
            smartAdjustment: Boolean,
        ) {
            val intent = Intent(context, TrainActivity::class.java)
            intent.putExtra("trainListData", trainListData)
            intent.putExtra("trainSceneData", trainSceneData)
            intent.putExtra("smartAdjustment", smartAdjustment)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_train

    override fun initView() {
        model = ViewModelProvider(this)[TrainViewModel::class.java]
        binding.model = model

        setOnClickListener(
            R.id.ivBack,
            R.id.btnTrainIntro,
            R.id.btnStartTrainBig,
            R.id.btnStartTrain,
            R.id.btnDeviceManage,
            R.id.tvTrainChoose1,
            R.id.tvTrainChoose2,
            R.id.tvTrainChoose3,
            R.id.ll_smart
        )
        //vr 场景不播放视频
        if (mTrainSceneData.isVR) {
            binding.ivBgDefault.visible()
        } else {
            initVideoPlayer()
            binding.ivBgDefault.gone()
        }
        showRealTimeChat()

    }

    override fun initData() {
        model.setDefaultData(this, mTrainListData, mTrainSceneData, mSmartAdjustment)
        observeDataChange()
        observeDeviceConnectionStatus()
        //团体训练，自动开始训练
        if (mTrainListData.TrainType == TrainType.TEAM) {
            model.startTrain(this)
        }
    }

    //监听数据改变
    private fun observeDataChange() {
        //是否展示倒计时组件监听
        model.showReadyTimerText.observe(this) { v ->
            if (v) binding.btnStartTrainBig.gone()
        }
        //是否跳转到设备管理界面监听
        model.startDeviceManagerActivityFlag.observe(this) { v ->
            if (v) startActivityForResult(
                DeviceManagerActivity::class.java,
                onDeviceManagerCallBack
            )
        }
        //训练状态监听
        model.isTraining.observe(this) { v ->
            if (v) {
                showTrainingView()
                if (!trainStarted) {
                    trainStarted = true
                    //开始训练的时候调用这个接口
                    model.requestBeforeAndAfterTrain(true)
                }
            } else {
                showTrainPauseView()
            }
        }
        //训练完成监听
        model.completeTrain.observe(this) { v ->
            if (v) {
                binding.tvTrainState.text = "训练结束"
                DialogUtil.showDialogWithConfirmCallBack(
                    this,
                    "提示",
                    "恭喜，您已完成本次训练!"
                ) { dialog, _ ->
                    dialog.dismiss()
                    finish()
                }
            }
        }
        //指脉数据监听
        TrainData.fingerDeviceData.observe(this) { data ->
            if (model.isTraining.value == true && data != null) {
                model.saveFingerData(this@TrainActivity,data)
                if (AppConfig.enableChart) {
                    binding.tvTrainHeart.text = "心率：${data.heartRate}"
                    if (data.heartRate.toFloat() > 0) addEntry(
                        heartLineData,
                        binding.lineChartHeart,
                        data.heartRateWave.toFloat()
                    )
                }
            }
        }
        //单车数据监听
        TrainData.bikeDeviceData.observe(this) { data ->
            if (model.isTraining.value == true && data != null) {
                model.saveBikeData(data)
                toggleVideoState(data.speedKMH != 0.0)
            }
        }

        //呼吸数据监听
        TrainData.breathDeviceData.observe(this) { data ->
            if (model.isTraining.value == true && data != null) {
                model.saveBreathData(data)
                if (AppConfig.enableChart) {
                    binding.tvTrainBreath.text = "呼吸：${data.freq}"
                    if (data.freq > 0) addEntry(
                        breathLineData,
                        binding.lineChartBreath,
                        data.freq.toFloat()
                    )
                }
            }
        }
    }

    /**
     * 监听设备连接状态
     */
    private fun observeDeviceConnectionStatus() {
        val pauseTrainAndNavigateToDeviceManager: (String) -> Unit = { deviceName ->
            if (ActivityManager.getInstance().getTopActivity() is TrainActivity) {
                model.pauseTrain(this)
                DialogUtil.showDialogWithConfirmCallBack(
                    this,
                    "提示",
                    "${deviceName}设备断开连接，请重新连接"
                ) { dialog, _ ->
                    dialog.dismiss()
                    startActivityForResult(
                        DeviceManagerActivity::class.java,
                        onDeviceManagerCallBack
                    )
                }
            }
        }

        TrainData.fingerDisConnected.observe(this) { v ->
            if (v && model.isTraining.value == true) pauseTrainAndNavigateToDeviceManager("指脉")
        }

        TrainData.bikeDisConnected.observe(this) { v ->
            if (v && model.isTraining.value == true) pauseTrainAndNavigateToDeviceManager("单车")
        }

        TrainData.breathDisConnected.observe(this) { v ->
            if (v && model.isTraining.value == true) pauseTrainAndNavigateToDeviceManager("呼吸")
        }
    }

    /**
     * 显示训练场景
     *
     */
    private fun showTrainingView() {
        if (!mTrainSceneData.isVR) {
            mPlayer?.play()
        }
        binding.btnStartTrainBig.gone()
        binding.btnStartTrain.text = "暂停训练"
        binding.tvTrainState.text = "训练中"
    }

    /**
     * 显示训练暂停场景
     */
    private fun showTrainPauseView() {
        if (!mTrainSceneData.isVR) {
            mPlayer?.pause()
        }
        binding.btnStartTrainBig.visible()
        binding.btnStartTrain.text = "开始训练"
        binding.tvTrainState.text = "训练暂停"
    }

    /**
     * 初始化视频播放器
     */
    private fun initVideoPlayer() {
        mPlayer = ExoPlayer.Builder(this).build()
        binding.playerView.player = mPlayer
        val mediaItem = MediaItem.Builder()
            .setUri("asset:///video/${mTrainSceneData.fileName}")
            .build()
        mPlayer?.setMediaItem(mediaItem)
        mPlayer?.volume = 0f
        mPlayer?.repeatMode = ExoPlayer.REPEAT_MODE_ONE
        mPlayer?.prepare()
        mPlayer?.playWhenReady = false
    }

    /**
     * 切换视频播放状态
     */
    private fun toggleVideoState(play: Boolean) {
        if (mTrainSceneData.isVR) return
        if (play) {
            mPlayer?.play()
        } else {
            mPlayer?.pause()
        }
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ivBack -> onBackPressed()

            R.id.btnTrainIntro -> showTrainIntro()

            R.id.btnStartTrain, R.id.btnStartTrainBig -> {
                if (model.isTraining.value == true) {
                    model.pauseTrain(this@TrainActivity)
                } else {
                    model.startTrain(this@TrainActivity)
                }
            }

            R.id.btnDeviceManage -> {
                PermissionsUtils.requestBlePermission(this) {
                    if (binding.tvReadyTimer.isGone) {
                        startActivityForResult(
                            DeviceManagerActivity::class.java,
                            onDeviceManagerCallBack
                        )
                    }
                }
            }

            R.id.tvTrainChoose1 -> showTrainChoose(1)

            R.id.tvTrainChoose2 -> showTrainChoose(2)

            R.id.tvTrainChoose3 -> showTrainChoose(3)

            R.id.ll_smart -> {
                DialogUtil.showDialogWithSmartAdjustment(this, { dialog, _ ->
                    dialog.dismiss()
                    model.smartAdjustment.value = false
                }, { dialog, _ ->
                    dialog.dismiss()
                    model.smartMinTime = 0
                    model.smartMaxTime = 0
                    model.smartAdjustment.value = true
                })
            }
        }
    }


    /**
     * 训练说明
     */
    private fun showTrainIntro() {
        DialogUtil.showDialogWithConfirmCallBack(
            this,
            "训练说明",
            mTrainListData.TrainNotice
        ) { dialog, _ ->
            dialog.dismiss()
            getStatusBarConfig().init()
        }
    }

    /**
     * 蓝牙管理界面回调
     */
    private val onDeviceManagerCallBack = object : OnActivityCallback {
        override fun onActivityResult(resultCode: Int, data: Intent?) {
            if (resultCode == RESULT_OK) {
                model.startTrain(this@TrainActivity)
            }
        }
    }

    /**
     * 判断显示实时图表
     */
    private fun showRealTimeChat() {
        if (AppConfig.enableChart) {
            binding.apply {
                binding.llTrainChoose2.visibility = View.VISIBLE
                llTrainChoose3.visibility = View.VISIBLE
                llChatNumber.visibility = View.VISIBLE
                showTrainChoose(1)
                //初始化图表
                initLineChat(lineChartHeart)
                initLineChat(lineChartBreath)
                //设置两个图表的默认数据
                setChartDefaultData(lineChartHeart)
                setChartDefaultData(lineChartBreath)
            }

        } else {
            binding.apply {
                llTrainChoose2.visibility = View.GONE
                llTrainChoose3.visibility = View.GONE
                llChatNumber.visibility = View.GONE
            }
        }
    }

    private fun showTrainChoose(type: Int) {
        binding.apply {
            when (type) {
                1 -> {
                    tvTrainChoose1.isSelected = true
                    tvTrainChoose2.isSelected = false
                    tvTrainChoose3.isSelected = false
                    llTrainChoose1.visibility = View.VISIBLE
                    llTrainChoose2.visibility = View.GONE
                    llTrainChoose3.visibility = View.GONE
                }

                2 -> {
                    tvTrainChoose1.isSelected = false
                    tvTrainChoose2.isSelected = true
                    tvTrainChoose3.isSelected = false
                    llTrainChoose1.visibility = View.GONE
                    llTrainChoose2.visibility = View.VISIBLE
                    llTrainChoose3.visibility = View.GONE
                }

                3 -> {
                    tvTrainChoose1.isSelected = false
                    tvTrainChoose2.isSelected = false
                    tvTrainChoose3.isSelected = true
                    llTrainChoose1.visibility = View.GONE
                    llTrainChoose2.visibility = View.GONE
                    llTrainChoose3.visibility = View.VISIBLE
                }
            }
        }
    }

    /**
     * 新增，初始化图表
     */
    private fun initLineChat(chart: LineChart) {
        // 图表样式
        chart.apply {
            setNoDataText("暂无数据")
            setDrawGridBackground(false) // 不展示网格背景
            setDrawBorders(false) // 不显示边框
            isDragEnabled = true // 可拖动
            setScaleEnabled(false) // 禁止缩放
            setTouchEnabled(false) // 不响应触摸事件
            description.isEnabled = false // 不显示描述
            setBorderWidth(0.5f) // 边框宽度
            setBorderColor("#666666".toColorInt()) // 边框颜色
        }
        // 设置xy轴
        chart.xAxis.apply {
            position = XAxis.XAxisPosition.BOTTOM // X轴显示在底部
            setDrawLabels(false) // 不绘制值
            setDrawGridLines(false) // 不绘制轴网格线
            setDrawAxisLine(true) // 绘制轴线
            axisLineColor = "#FFFFFF".toColorInt()
            axisMinimum = 0f // x轴最小值
            axisMaximum = Float.MAX_VALUE // x轴最大值
            setLabelCount(20, false) // x轴刻度数量
            textSize = 4f // 轴上的值字体大小
            granularity = if (chart == binding.lineChartHeart) 1f else 15f // x轴坐标之间的最小间隔
            chart.setVisibleXRangeMaximum(20f) //X轴最多显示出来的总量
        }
        chart.axisLeft.apply {
            setDrawGridLines(false) // 不绘制轴网格线
            setDrawAxisLine(true) //绘制轴线
            axisLineColor = "#FFFFFF".toColorInt()
            axisMinimum = if (chart == binding.lineChartHeart) 0f else 0f // y轴最小值
            axisMaximum = if (chart == binding.lineChartHeart) 160f else 80f //y轴最大值,心率180,呼吸50
            granularity = 1f // 刻度
            textSize = 6f // 轴上的值字体大小
            textColor = "#FFFFFF".toColorInt()
            labelCount = 5 // 轴上的值数量
            isEnabled = true // 启用
        }

        chart.axisRight.isEnabled = false

        //禁止图例
        chart.legend.isEnabled = false
    }

    /**
     * 设置图表默认数据
     */
    private fun setChartDefaultData(chart: LineChart) {
        if (chart == binding.lineChartHeart) {
            heartLineData = LineData()
            chart.data = heartLineData

            setLineStyle("#FECB33".toColorInt(), heartDataSet)
            chart.lineData.addDataSet(heartDataSet)
            //addEntry(heartLineData, chart, 0f)
        } else {
            breathLineData = LineData()
            chart.data = breathLineData

            setLineStyle("#FECB33".toColorInt(), breathDataSet)
            chart.lineData.addDataSet(breathDataSet)
            //addEntry(breathLineData, chart, 0f)
        }
    }

    /**
     * 设置图表中折线的样式
     */
    private fun setLineStyle(colour: Int, dataSet: LineDataSet) {
        dataSet.apply {
            color = colour
            setDrawCircles(false)
            setDrawValues(false)
            setDrawFilled(false)
            lineWidth = 1f
            formSize = 15f
            mode = LineDataSet.Mode.CUBIC_BEZIER
        }
    }

    /**
     * 增加一条数据
     */
    private fun addEntry(lineData: LineData, chart: LineChart, yValue: Float) {
        val xCount = lineData.getDataSetByIndex(0).entryCount.toFloat()
        val entry = Entry(xCount, yValue)
        lineData.addEntry(entry, 0)

        lineData.notifyDataChanged()
        chart.notifyDataSetChanged()

        chart.moveViewTo(xCount - 20, yValue, YAxis.AxisDependency.LEFT)
    }


    override fun onBackPressed() {
        if (heartDataSet.values.size > 1) {
            DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
                this,
                "提示",
                "训练还未完成，现在退出将无法保存数据，确定要退出吗？",
                { dialog, _ ->
                    dialog.dismiss()
                    super.onBackPressed()
                })
        } else {
            super.onBackPressed()
        }
    }

    override fun onPause() {
        super.onPause()
        if (model.isTraining.value == true && !model.trainSceneData.isVR && model.trainListData.TrainType != TrainType.TEAM) {
            model.pauseTrain(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
//        BleManager.get().getAllConnectedDevice()?.forEach {
//            BleManager.get().removeBleConnectCallback(it)
//        }
//        BleManager.get().disConnectAll()
        TrainData.reset()
        mPlayer?.release()
    }

}