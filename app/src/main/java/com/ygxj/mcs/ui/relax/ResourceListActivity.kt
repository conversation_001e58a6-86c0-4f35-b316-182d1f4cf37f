package com.ygxj.mcs.ui.relax


import android.content.Context
import android.content.Intent
import android.view.View
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.net.Post
import com.drake.net.utils.scope
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.SmartGlideImageLoader
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityResourceListBinding
import com.ygxj.mcs.databinding.ItemImageListBinding
import com.ygxj.mcs.databinding.ItemMusicListBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.ResourceData
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.ui.dialog.DialogInputIp
import com.ygxj.mcs.utils.AudioPlayer
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.loadImage2
import com.ygxj.mcs.utils.visible
import java.io.DataOutputStream
import java.net.InetSocketAddress
import java.net.Socket


/**
 * 资源列表界面
 */
class ResourceListActivity : BaseActivity<ActivityResourceListBinding>() {

    private val musicCovers = arrayListOf(
        R.drawable.music_img_1,
        R.drawable.music_img_2,
        R.drawable.music_img_3,
        R.drawable.music_img_4,
        R.drawable.music_img_5,
        R.drawable.music_img_6,
        R.drawable.music_img_7,
        R.drawable.music_img_8,
        R.drawable.music_img_9,
        R.drawable.music_img_10,
        R.drawable.music_img_11,
        R.drawable.music_img_12,
        R.drawable.music_img_13,
        R.drawable.music_img_14,
        R.drawable.music_img_15,
        R.drawable.music_img_16,
        R.drawable.music_img_17,
        R.drawable.music_img_18,
        R.drawable.music_img_19
    )

    // 0音乐列表 1图片列表  2VR列表
    private val mType by lazy { intent.getIntExtra("type", 0) }

    //图片放松的图片url集合
    private var imageUrls = listOf<String>()

    private var socket: Socket? = null

    private var writer: DataOutputStream? = null

    companion object {
        fun start(context: Context, type: Int) {
            val intent = Intent(context, ResourceListActivity::class.java)
            intent.putExtra("type", type)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_resource_list

    override fun initView() {
        setOnClickListener(R.id.tvConfig)
        initRecyclerView()

        when (mType) {
            0 -> binding.tvResourceType.text = "音乐放松体验"
            1 -> binding.tvResourceType.text = "图片放松体验"
            2 -> {
                binding.tvResourceType.text = "深度放松体验"
                binding.llVrIpConfigView.visible()
                if (!Api.vrIp.isNullOrBlank()) {
                    binding.tvVrIp.text = "当前VR设备地址：${Api.vrIp}"
                }
            }
        }
    }

    override fun initData() {
        binding.state.onRefresh {
            scope {
                val url = when (mType) {
                    0 -> Api.POST_MUSIC_LIST
                    1 -> Api.POST_IMAGE_LIST
                    2 -> Api.POST_VR_LIST
                    else -> ""
                }
                val resp = Post<List<ResourceData>>(url).await()
                if (resp.isEmpty()) {
                    showEmpty()
                } else {
                    showContent()
                    binding.rv.models = resp
                    imageUrls = resp.map { Api.HOST + it.ImageUrl }
                    if (mType==0){
                        AudioPlayer.musicList = resp
                    }
                }
            }
        }.showLoading()
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvConfig -> {
                if (mType == 2) {
                    showConfigVrIpDialog()
                }
            }
        }
    }

    /**
     * 初始化RecyclerView
     */
    private fun initRecyclerView() {
        binding.rv.setup {
            if (mType == 0) {
                addType<ResourceData>(R.layout.item_music_list)
            } else {
                addType<ResourceData>(R.layout.item_image_list)
            }
            onBind {
                when (mType) {
                    0 -> {
                        val item = getBinding<ItemMusicListBinding>()
                        item.ivMusicCover.setImageResource(musicCovers.random())
                    }

                    1 -> {
                        val item = getBinding<ItemImageListBinding>()
                        item.ivImage.loadImage2(getModel<ResourceData>().ImageUrl)
                        item.tvImageTitle.text = getModel<ResourceData>().ImageName
                    }

                    2 -> {
                        val item = getBinding<ItemImageListBinding>()
                        item.tvImageTitle.text = getModel<ResourceData>().VRName
                        val imgId = when {
                            getModel<ResourceData>().VRName.contains("公路场景") -> R.drawable.music_img_20
                            getModel<ResourceData>().VRName.contains("鲜花大道") -> R.drawable.music_img_12
                            getModel<ResourceData>().VRName.contains("崎岖山路") -> R.drawable.music_img_17
                            else -> R.drawable.music_img_10
                        }
                        item.ivImage.setImageResource(imgId)
                    }
                }
            }

            onClick(R.id.item) {
                when (mType) {
                    0 -> MusicPlayActivity.start(this@ResourceListActivity, getModel(),layoutPosition)

                    1 -> {
                        XPopup.Builder(getContext()).isViewMode(true).asImageViewer(
                            null, layoutPosition, imageUrls, null, SmartGlideImageLoader()
                        ).show()
                    }

                    2 -> {
                        if (Api.vrIp.isNullOrBlank()) {
                            showConfigVrIpDialog()
                        } else {
                            connectVrDevice(getModel<ResourceData>().VRName)
                        }
                    }
                }
            }
        }
    }

    /**
     * 显示Vr设备Ip地址配置弹窗
     */
    private fun showConfigVrIpDialog() {
        DialogInputIp(this) { ip ->
            Api.vrIp = ip
            binding.tvVrIp.text = "当前VR设备地址：$ip"
        }.build().show()
    }

    /**
     * 连接VR设备
     */
    private fun connectVrDevice(sceneName: String) {
        // 发起socket连接
        DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
            this,
            "提示",
            "请打开VR设备和场景程序，点击确定按钮进行连接。",
            { dialog, _ ->
                dialog.dismiss()
                val loadDialog =
                    DialogUtil.showLoadingDialog(this@ResourceListActivity, "正在连接VR设备")
                object : Thread() {
                    override fun run() {
                        try {
                            if (socket == null) socket = Socket()
                            if (!socket!!.isConnected) socket?.connect(
                                InetSocketAddress(
                                    Api.vrIp,
                                    5566
                                ), 5 * 1000
                            )
                            if (socket!!.isConnected) {
                                if (writer == null) {
                                    writer = DataOutputStream(socket!!.getOutputStream())
                                }
                                writer!!.write(sceneName.toByteArray())
                                Toaster.show("VR设备已连接，可以开始放松体验了！")
                            } else {
                                Toaster.show("VR设备连接失败，请确保设备已打开，稍后重新尝试")
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            Toaster.show("VR设备连接失败，请确保设备已打开，稍后重新尝试")
                            socket?.close()
                            writer?.close()
                            socket = null
                            writer = null
                        } finally {
                            loadDialog.dismiss()
                        }
                    }
                }.start()
            })
    }

    override fun onDestroy() {
        super.onDestroy()
        socket?.close()
        writer?.close()
    }

}