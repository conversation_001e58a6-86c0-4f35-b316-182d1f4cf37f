package com.ygxj.mcs.ui.main


import android.content.Context
import android.content.Intent
import android.view.View
import com.angcyo.tablayout.delegate.ViewPager1Delegate
import com.bhm.ble.BleManager
import com.drake.channel.receiveEvent
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityMainPageBinding
import com.ygxj.mcs.other.Constants
import com.ygxj.mcs.other.FragmentPagerAdapter
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.ui.base.BaseFragment



/**
 * 主页面
 */
class MainPageActivity : BaseActivity<ActivityMainPageBinding>() {

    private val mIndex by lazy { intent.getIntExtra("index", 0) }

    private var pagerAdapter: FragmentPagerAdapter<BaseFragment<*, *>>? = null

    companion object {
        fun start(context: Context, index: Int) {
            val intent = Intent(context, MainPageActivity::class.java)
            intent.putExtra("index", index)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_main_page

    override fun initView() {
        pagerAdapter = FragmentPagerAdapter<BaseFragment<*, *>>(this).apply {
            addFragment(EvaluateFragment.newInstance())
            addFragment(TrainFragment.newInstance())
            addFragment(DataFragment.newInstance())
            addFragment(RelaxFragment.newInstance())
            addFragment(UserFragment.newInstance())
            binding.vp.adapter = this
        }
        //禁止vp滑动
        binding.vp.setSwipeable(false)
        //关联vp和tabLayout
        ViewPager1Delegate.install(binding.vp,binding.tabLayout,false)
        binding.vp.currentItem = mIndex
    }

    override fun initData() {
//        receiveEvent<String> {
//            if (it == Constants.SWITCH_TO_SCALE){
//                binding.vp.currentItem = 0
//            }
//        }
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {

        }
    }


    /**
     * 切换vp
     */
     fun switchToIndex(index: Int){
        binding.vp.currentItem = index
    }

    override fun onDestroy() {
        super.onDestroy()
        BleManager.get().disConnectAll()
    }
}