package com.ygxj.mcs.ui.data


import android.content.Context
import android.content.Intent
import android.graphics.Color
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.net.Post
import com.drake.net.utils.scope
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityTeamDataDetailBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.TrainResultData
import com.ygxj.mcs.ui.base.BaseActivity

/**
 * 团体数据详情
 */
class TeamDataDetailActivity : BaseActivity<ActivityTeamDataDetailBinding>() {

    private val mTrainResultData by lazy { intent.getSerializableExtra("trainListData") as TrainResultData }

    companion object {
        fun start(context: Context, trainResultData: TrainResultData) {
            val intent = Intent(context, TeamDataDetailActivity::class.java)
            intent.putExtra("trainListData", trainResultData)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_team_data_detail

    override fun initView() {
        initChart(binding.chartHeartRate)
        initChart(binding.chartSpeed)
        initChart(binding.chartOxygenConsumption)
        initChart(binding.chartBreath)
        binding.rv.setup {
            addType<TrainResultData>(R.layout.item_room_user)
        }
    }

    override fun initData() {
        loadProgressData()
        loadChartHeartData()
        loadChartSpeedData()
        loadChartOxygenData()
        loadChartBreathData()
        binding.tvAdvice.text = mTrainResultData.ResultPingjia
        binding.tvDataUserName.text = mTrainResultData.UserName + "的训练数据"
        binding.state.onRefresh {
            scope {
                val resp = Post<List<TrainResultData>>(Api.POST_ROOM_INFO) {
                    param("roomid", mTrainResultData.RoomID)
                }.await()
                if (resp.isEmpty()) {
                    showEmpty()
                } else {
                    showContent()
                    binding.rv.models = resp
                }
            }
        }.showLoading()
    }

    /**
     * 加载进度条数据
     */
    private fun loadProgressData() {
        binding.mcsHeartRate.progress = getAvgFromList(mTrainResultData.HeartRateList).toInt() / 100f
        binding.tvHeartRate.text = getAvgFromList(mTrainResultData.HeartRateList).toInt().toString()

        binding.mcsSpeed.progress = mTrainResultData.AvgSpeed.toInt() / 100f
        binding.tvSpeed.text = mTrainResultData.AvgSpeed.toInt().toString()

        binding.mcsOxygenConsumption.progress =
            getAvgFromList(mTrainResultData.OxygenConsumptionList).toInt() / 100f
        binding.tvOxygenConsumption.text =
            getAvgFromList(mTrainResultData.OxygenConsumptionList).toInt().toString()

        binding.mcsCalories.progress = mTrainResultData.Calorie.toInt() / 100f
        binding.tvCalories.text = mTrainResultData.Calorie.toInt().toString()

        binding.mcsBreath.progress = getAvgFromList(mTrainResultData.HxList).toInt() / 100f
        binding.tvBreath.text = getAvgFromList(mTrainResultData.HxList).toInt().toString()
    }

    /**
     * 加载心率图表数据
     */
    private fun loadChartHeartData() {
        val dataList: List<String>
        val entryList = arrayListOf<Entry>()
        if (mTrainResultData.HeartRateList.isNotBlank()) {
            dataList = mTrainResultData.HeartRateList.split(",")
            for (i in dataList.indices) entryList.add(Entry(i * 1f, dataList[i].toFloat()))
        }
        binding.chartHeartRate.data = getChartData(entryList)
    }

    /**
     * 加载速度图表数据
     */
    private fun loadChartSpeedData() {
        val dataList: List<String>
        val entryList = arrayListOf<Entry>()
        if (mTrainResultData.OxygenConsumptionList.isNotBlank()) {
            dataList = mTrainResultData.OxygenConsumptionList.split(",")
            for (i in dataList.indices) entryList.add(Entry(i * 1f, dataList[i].toFloat()))
        }
        binding.chartSpeed.data = getChartData(entryList)
    }

    /**
     * 加载耗氧图表数据
     */
    private fun loadChartOxygenData() {
        val dataList: List<String>
        val entryList = arrayListOf<Entry>()
        if (mTrainResultData.HeartRateList.isNotBlank()) {
            dataList = mTrainResultData.HeartRateList.split(",")
            for (i in dataList.indices) entryList.add(Entry(i * 1f, dataList[i].toFloat()))
        }
        binding.chartOxygenConsumption.data = getChartData(entryList)
    }

    /**
     * 加载呼吸图表数据
     */
    private fun loadChartBreathData() {
        val dataList: List<String>
        val entryList = arrayListOf<Entry>()
        if (mTrainResultData.HxList.isNotBlank()) {
            dataList = mTrainResultData.HxList.split(",")
            for (i in dataList.indices) entryList.add(Entry(i * 1f, dataList[i].toFloat()))
        }
        binding.chartBreath.data = getChartData(entryList)
    }

    /**
     * 设置LineData
     */
    private fun getChartData(entryList: ArrayList<Entry>): LineData {
        val set = LineDataSet(entryList, "曲线图")
        set.mode = LineDataSet.Mode.CUBIC_BEZIER
        set.cubicIntensity = 0.2f
        set.setDrawFilled(true)
        set.setDrawCircles(false)
        set.lineWidth = 0.2f
        set.circleRadius = 4f
        set.color = Color.parseColor("#1065e5")
        set.fillColor = Color.parseColor("#1065e5")
        set.fillAlpha = 127
        set.setDrawHorizontalHighlightIndicator(false)
        val data = LineData(set)
        data.setValueTextSize(9f)
        data.setDrawValues(false)
        return data
    }

    /**
     * 初始化图表控件
     */
    private fun initChart(chart: LineChart) {
        chart.setNoDataText("暂无数据")
        chart.description.isEnabled = false
        chart.setTouchEnabled(false)
        chart.isDragEnabled = false
        chart.setScaleEnabled(false)
        chart.setPinchZoom(false)
        chart.setDrawGridBackground(false)
        chart.maxHighlightDistance = 300f

        //配置x轴
        chart.xAxis.isEnabled = false
        //配置y轴
        val yAxis = chart.axisLeft
        yAxis.enableGridDashedLine(10f, 10f, 0f)
        yAxis.setDrawLabels(true)
        yAxis.setLabelCount(6, false)
        yAxis.textColor = getColor(R.color.colorPrimary)
        yAxis.setDrawGridLines(true)
        yAxis.axisMinimum = 0f
        yAxis.gridColor = getColor(R.color.colorPrimary)
        yAxis.axisLineColor = getColor(R.color.colorPrimary)
        chart.axisRight.isEnabled = false
        //禁止图例
        chart.legend.isEnabled = false

        chart.animateXY(0, 700)
        chart.invalidate()
    }

    /**
     * 计算平均值
     */
    private fun getAvgFromList(listString: String?): Float {
        if (listString.isNullOrBlank()) {
            return 0f
        }
        val list = listString.split(",")
        var sum = 0f
        for (i in list) {
            sum += i.trim().toFloat()
        }
        return sum / list.size
    }

}