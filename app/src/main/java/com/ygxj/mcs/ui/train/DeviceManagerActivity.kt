package com.ygxj.mcs.ui.train


import android.bluetooth.BluetoothGattCharacteristic
import android.content.Context
import android.content.Intent
import android.view.View
import androidx.core.view.isGone
import androidx.lifecycle.lifecycleScope
import com.bhm.ble.BleManager
import com.bhm.ble.data.BleConnectFailType
import com.bhm.ble.data.BleDescriptorGetType
import com.bhm.ble.data.BleScanFailType
import com.bhm.ble.device.BleDevice
import com.bhm.ble.log.BleLogger
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.tencent.mmkv.MMKV
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityDeviceManagerBinding
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.utils.BikeDataUtil
import com.ygxj.mcs.utils.BreathDataUtil
import com.ygxj.mcs.utils.FingerDataUtil
import com.ygxj.mcs.utils.Hex
import com.ygxj.mcs.utils.PermissionsUtils
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.invisible
import com.ygxj.mcs.utils.visible
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 蓝牙设备管理页面
 */
class DeviceManagerActivity : BaseActivity<ActivityDeviceManagerBinding>() {

    //指脉
    private var mFingerDevice: BleDevice? = null

    //单车
    private var mBikeDevice: BleDevice? = null

    //呼吸
    private var mBreathDevice: BleDevice? = null

    private var job: Job? = null

    companion object {
        fun start(context: Context) {
            val intent = Intent(context, DeviceManagerActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_device_manager

    override fun initView() {
        setOnClickListener(
            R.id.btnClearHistory,
            R.id.btnSearchDevice,
            R.id.btnGoingTrain,
            R.id.ivShowLogo,
            R.id.tvFingerDeviceState,
            R.id.tvBikeDeviceState,
            R.id.tvBreathDeviceState,
        )
    }

    override fun initData() {
        binding.tvState.text = "点击搜索按钮，开始搜索设备"
        binding.tvFingerDeviceName.text = FingerDataUtil.fingerName
        binding.tvBikeDeviceName.text = BikeDataUtil.bikeName
        binding.tvBreathDeviceName.text = BreathDataUtil.breathName

        setDefaultFingerState()
        setDefaultBikeState()
        setDefaultBreathState()
    }

    /**
     * 设置指脉设备默认状态
     */
    private fun setDefaultFingerState() {
        if (FingerDataUtil.fingerMac.isNullOrEmpty()) {
            binding.rlFingerDevice.gone()
        } else {
            binding.tvState.gone()
            binding.rlFingerDevice.visible()
            mFingerDevice =
                BleManager.get().buildBleDeviceByDeviceAddress(FingerDataUtil.fingerMac.toString())
            if (BleManager.get().isConnected(FingerDataUtil.fingerMac.toString())) {
                //设置连接状态监听
                binding.tvFingerDeviceState.text = "已连接"
                setDefaultOnConnected(FingerDataUtil.fingerMac.toString())
            } else {
                startConnect(mFingerDevice!!)
            }
        }
    }

    /**
     * 设置单车设备默认状态
     */
    private fun setDefaultBikeState() {
        if (BikeDataUtil.bikeMac.isNullOrEmpty()) {
            binding.rlBikeDevice.gone()
        } else {
            binding.rlBikeDevice.visible()
            mBikeDevice =
                BleManager.get().buildBleDeviceByDeviceAddress(BikeDataUtil.bikeMac.toString())
            if (BleManager.get().isConnected(BikeDataUtil.bikeMac.toString())) {
                binding.tvBikeDeviceState.text = "已连接"
                //设置连接状态监听
                setDefaultOnConnected(BikeDataUtil.bikeMac.toString())
            } else {
                startConnect(mBikeDevice!!)
            }
        }
    }

    /**
     * 设置呼吸设备默认状态
     */
    private fun setDefaultBreathState() {
        if (BreathDataUtil.breathMac.isNullOrEmpty()) {
            binding.rlBreathDevice.gone()
        } else {
            binding.rlBreathDevice.visible()
            mBreathDevice =
                BleManager.get().buildBleDeviceByDeviceAddress(BreathDataUtil.breathMac.toString())
            if (BleManager.get().isConnected(BreathDataUtil.breathMac.toString())) {
                binding.tvBreathDeviceState.text = "已连接"
                //设置连接状态监听
                setDefaultOnConnected(BreathDataUtil.breathMac.toString())
            } else {
                startConnect(mBreathDevice!!)
            }
        }
    }

    /**
     * 设置默认的连接状态监听
     */
    private fun setDefaultOnConnected(address: String) {
        BleManager.get().replaceBleConnectCallback(address) {
            onDisConnected { isActiveDisConnected, bleDevice, gatt, status ->
                disConnected(bleDevice)
            }
        }
    }

    /**
     * 点击事件
     */
    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnClearHistory -> {
                //清除历史记录
                FingerDataUtil.clearDeviceData()
                BleManager.get().disConnectAll()
                binding.rlFingerDevice.gone()
                binding.rlBikeDevice.gone()
                binding.rlBreathDevice.gone()
                binding.btnClearHistory.gone()
            }

            R.id.btnSearchDevice -> {
                PermissionsUtils.requestBlePermission(this) {
                    startScanDevice()
                }
            }


            R.id.btnGoingTrain -> {
                //开始训练 先判断设备连接状态，单车,指脉必须连接，呼吸设备可以不连接
                val bikeConnected =
                    BikeDataUtil.bikeMac?.let { BleManager.get().isConnected(it) } ?: false
                if (!bikeConnected) {
                    Toaster.show("请连接单车设备")
                    return
                }

                val fingerConnected =
                    FingerDataUtil.fingerMac?.let { BleManager.get().isConnected(it) } ?: false
                if (!fingerConnected) {
                    Toaster.show("请连接指脉设备")
                    return
                }
                finish()
            }

            R.id.ivShowLogo -> {
                //显示logo
                if (binding.btnClearHistory.isGone) {
                    binding.btnClearHistory.visible()
                } else {
                    binding.btnClearHistory.gone()
                }
            }

            //连接指脉
            R.id.tvFingerDeviceState -> {
                if (binding.tvFingerDeviceState.text.contains("连接失败")) {
                    mFingerDevice?.let {
                        startConnect(it)
                    }
                }
            }
            //连接单车
            R.id.tvBikeDeviceState -> {
                if (binding.tvBikeDeviceState.text.contains("连接失败")) {
                    mBikeDevice?.let {
                        startConnect(it)
                    }
                }
            }
            //连接呼吸
            R.id.tvBreathDeviceState -> {
                if (binding.tvBreathDeviceState.text.contains("连接失败")) {
                    mBreathDevice?.let {
                        startConnect(it)
                    }
                }
            }
        }
    }

    /**
     * 开始扫描设备
     */
    private fun startScanDevice() {
        BleManager.get().startScan {
            onScanStart {
                showLoadingDialog("设备搜索中")
            }
            onLeScan { bleDevice, currentScanCount ->
                //可以根据currentScanCount是否已有清空列表数据
            }
            onLeScanDuplicateRemoval { bleDevice, currentScanCount ->
                //与onLeScan区别之处在于：同一个设备只会出现一次

            }
            onScanComplete { bleDeviceList, bleDeviceDuplicateRemovalList ->
                //扫描到的数据是所有扫描次数的总和
                hideLoadingDialog()
                handleScanResult(bleDeviceDuplicateRemovalList)
            }
            onScanFail {
                val msg: String = when (it) {
                    is BleScanFailType.UnSupportBle -> "BleScanFailType.UnSupportBle: 设备不支持蓝牙"
                    is BleScanFailType.NoBlePermission -> "BleScanFailType.NoBlePermission: 权限不足，请检查"
                    is BleScanFailType.GPSDisable -> "BleScanFailType.BleDisable: 设备未打开GPS定位"
                    is BleScanFailType.BleDisable -> "BleScanFailType.BleDisable: 蓝牙未打开"
                    is BleScanFailType.AlReadyScanning -> "BleScanFailType.AlReadyScanning: 正在扫描"
                    is BleScanFailType.ScanError -> {
                        "BleScanFailType.ScanError: ${it.throwable?.message}"
                    }
                }
                BleLogger.e(msg)
                Toaster.show(msg)
                hideLoadingDialog()
            }
        }
    }

    /**
     * 处理扫描结果
     */
    private fun handleScanResult(deviceList: MutableList<BleDevice>) {
        binding.tvState.visible()
        if (deviceList.isEmpty() && BikeDataUtil.bikeMac.isNullOrEmpty()) {
            binding.tvState.text = "未能找到设备，请确保设备处于开启状态后，尝试再次搜索"
            return
        }
        val fingerList =
            deviceList.filter { it.deviceName?.contains(FingerDataUtil.fingerName) == true }
        val bikeList = deviceList.filter { it.deviceName?.contains(BikeDataUtil.bikeName) == true }
        val breathList =
            deviceList.filter { it.deviceName?.contains(BreathDataUtil.breathName) == true }

        if (BikeDataUtil.bikeMac.isNullOrEmpty()) {
            if (fingerList.isEmpty()) {
                binding.tvState.text = "未能找到指脉设备，请确保设备处于开启状态后，尝试再次搜索"
                return
            }
            if (fingerList.size > 1) {
                binding.tvState.text = "找到多套指脉设备，请确保仅开启一套设备后，再次搜索尝试"
                return
            }
            if (bikeList.isEmpty()) {
                binding.tvState.text = "未能找到单车设备，请确保设备处于开启状态后，尝试再次搜索"
                return
            }
            if (bikeList.size > 1) {
                binding.tvState.text = "找到多套单车设备，请确保仅开启一套设备后，再次搜索尝试"
                return
            }
            if (breathList.isEmpty()) {
                binding.tvState.text = "未能找到呼吸设备，请确保设备处于开启状态后，再次搜索尝试"
                return
            }
            if (breathList.size > 1) {
                binding.tvState.text = "找到多套呼吸设备，请确保仅开启一套设备后，再次搜索尝试"
                return
            }
        }

        binding.tvState.gone()
        if (FingerDataUtil.fingerMac.isNullOrEmpty()) {
            mFingerDevice = fingerList.first()
            binding.rlFingerDevice.visible()
            FingerDataUtil.fingerName = mFingerDevice!!.deviceName.toString()
            FingerDataUtil.fingerMac = mFingerDevice!!.deviceAddress.toString()
            binding.tvFingerDeviceName.text = FingerDataUtil.fingerName
            startConnect(mFingerDevice!!)
        }
        if (BikeDataUtil.bikeMac.isNullOrEmpty()) {
            mBikeDevice = bikeList.first()
            binding.rlBikeDevice.visible()
            BikeDataUtil.bikeName = mBikeDevice!!.deviceName.toString()
            BikeDataUtil.bikeMac = mBikeDevice!!.deviceAddress.toString()
            binding.tvBikeDeviceName.text = BikeDataUtil.bikeName
            startConnect(mBikeDevice!!)
        }
        if (BreathDataUtil.breathMac.isNullOrEmpty()) {
            mBreathDevice = breathList.first()
            binding.rlBreathDevice.visible()
            BreathDataUtil.breathName = mBreathDevice!!.deviceName.toString()
            BreathDataUtil.breathMac = mBreathDevice!!.deviceAddress.toString()
            binding.tvBreathDeviceName.text = BreathDataUtil.breathName
            startConnect(mBreathDevice!!)
        }

    }

    /**
     * 开始连接
     */
    private fun startConnect(bleDevice: BleDevice) {
        BleManager.get().removeBleEventCallback(bleDevice)
        BleManager.get().connect(bleDevice, false) {
            onConnectStart {
                BleLogger.e("-----onConnectStart")
                connectStart(it)
            }
            onConnectFail { bleDevice, connectFailType ->
                val msg: String = when (connectFailType) {
                    is BleConnectFailType.UnSupportBle -> "设备不支持蓝牙"
                    is BleConnectFailType.NoBlePermission -> "权限不足，请检查"
                    is BleConnectFailType.NullableBluetoothDevice -> "设备为空"
                    is BleConnectFailType.BleDisable -> "蓝牙未打开"
                    is BleConnectFailType.ConnectException -> "连接异常(${connectFailType.throwable.message})"
                    is BleConnectFailType.ConnectTimeOut -> "连接超时"
                    is BleConnectFailType.AlreadyConnecting -> "连接中"
                    is BleConnectFailType.ScanNullableBluetoothDevice -> "连接失败，扫描数据为空"
                }
                BleLogger.e(msg)
                connectFail(bleDevice)
            }
            onDisConnecting { isActiveDisConnected, bleDevice, _, _ ->
                BleLogger.e("-----${bleDevice.deviceAddress} -> onDisConnecting: $isActiveDisConnected")
            }
            onDisConnected { isActiveDisConnected, bleDevice, _, _ ->
                Timber.e("蓝牙连接已断开")
                disConnected(bleDevice)
            }
            onConnectSuccess { bleDevice, _ ->
                connectSuccess(bleDevice)
            }
        }
    }

    /**
     * 连接开始
     */
    private fun connectStart(bleDevice: BleDevice) {
        when (bleDevice.deviceAddress) {
            FingerDataUtil.fingerMac -> {
                binding.tvFingerDeviceState.text = "正在连接"
                binding.pbFingerDeviceState.visible()
            }

            BikeDataUtil.bikeMac -> {
                binding.tvBikeDeviceState.text = "正在连接"
                binding.pbBikeDeviceState.visible()
            }

            BreathDataUtil.breathMac -> {
                binding.tvBreathDeviceState.text = "正在连接"
                binding.pbBreathDeviceState.visible()
            }
        }
    }

    /**
     * 连接失败
     */
    private fun connectFail(bleDevice: BleDevice) {
        when (bleDevice.deviceAddress) {
            FingerDataUtil.fingerMac -> {
                binding.tvFingerDeviceState.text = "连接失败，点击重试"
                binding.pbFingerDeviceState.invisible()
            }

            BikeDataUtil.bikeMac -> {
                binding.tvBikeDeviceState.text = "连接失败，点击重试"
                binding.pbBikeDeviceState.invisible()
            }

            BreathDataUtil.breathMac -> {
                binding.tvBreathDeviceState.text = "连接失败，点击重试"
                binding.pbBreathDeviceState.invisible()
            }
        }
    }

    /**
     * 连接断开
     */
    private fun disConnected(bleDevice: BleDevice) {
        when (bleDevice.deviceAddress) {
            FingerDataUtil.fingerMac -> {
                binding.tvFingerDeviceState.text = "连接失败，点击重试"
                TrainData.fingerDisConnected.postValue(true)
            }

            BikeDataUtil.bikeMac -> {
                binding.tvBikeDeviceState.text = "连接失败，点击重试"
                TrainData.bikeDisConnected.postValue(true)
            }

            BreathDataUtil.breathMac -> {
                binding.tvBreathDeviceState.text = "连接失败，点击重试"
                TrainData.breathDisConnected.postValue(true)
            }
        }
    }

    /**
     * 连接成功
     */
    private fun connectSuccess(bleDevice: BleDevice) {
        when (bleDevice.deviceAddress) {
            FingerDataUtil.fingerMac -> {
                binding.tvFingerDeviceState.text = "已连接"
                binding.pbFingerDeviceState.invisible()
                startNotify(
                    bleDevice,
                    FingerDataUtil.fingerServiceUUID,
                    FingerDataUtil.fingerCharacterUUID
                )
            }

            BikeDataUtil.bikeMac -> {
                binding.tvBikeDeviceState.text = "已连接"
                binding.pbBikeDeviceState.invisible()

                // 按照协议说明,先发送切换指令将雅康私有协议转化成约定好的协议
                BleManager.get().writeData(
                    bleDevice,
                    BikeDataUtil.bikeServiceUUID,
                    BikeDataUtil.bikeWriteUUID,
                    Hex.hexStr2bytes(BikeDataUtil.transformProtocolCMD)
                ){

                }
                // 数据清零
                BleManager.get().writeData(
                    bleDevice,
                    BikeDataUtil.bikeServiceUUID,
                    BikeDataUtil.bikeWriteUUID,
                    Hex.hexStr2bytes(BikeDataUtil.resetDataCMD)
                ){

                }
                startNotify(
                    bleDevice,
                    BikeDataUtil.bikeServiceUUID,
                    BikeDataUtil.bikeCharacterUUID
                )
            }

            BreathDataUtil.breathMac -> {
                binding.tvBreathDeviceState.text = "已连接"
                binding.pbBreathDeviceState.invisible()
                startNotify(
                    bleDevice,
                    BreathDataUtil.breathServiceUUID,
                    BreathDataUtil.breathCharacterUUID
                )
            }
        }
    }

    /**
     * 开始Notify
     */
    private fun startNotify(bleDevice: BleDevice, serviceUUID: String, characteristicUUID: String) {
        BleManager.get().notify(
            bleDevice,
            serviceUUID,
            characteristicUUID,
            BleDescriptorGetType.Default
        ) {
            onNotifyFail { _, _, _ ->
                BleLogger.e("获取数据失败")
            }
            onNotifySuccess { _, _ ->
                when (bleDevice.deviceAddress) {
                    FingerDataUtil.fingerMac -> BleLogger.e("指脉获取数据成功")

                    BikeDataUtil.bikeMac -> BleLogger.e("单车获取数据成功")

                    BreathDataUtil.breathMac -> BleLogger.e("呼吸获取数据成功")
                }
                // 判断是否全部连接成功,全部连接成功就跳转到训练界面，自动开始训练
                val list = BleManager.get().getAllConnectedDevice()
                if (list?.size == 3) {
                    job?.cancel()
                    job = lifecycleScope.launch {
                        delay(500)
                        setResult(RESULT_OK)
                        finish()
                    }
                }
            }
            onCharacteristicChanged { bleDevice, _, value ->
                when (bleDevice.deviceAddress) {
                    FingerDataUtil.fingerMac -> TrainData.parseFingerData(value)

                    BikeDataUtil.bikeMac -> TrainData.parseBikeData(value)

                    BreathDataUtil.breathMac -> TrainData.parseBreathData(value)
                }
            }
        }
    }

    /**
     * 根据bleDevice拿到服务特征值数据
     * 获取notify时的serviceUUID和characteristicUUID
     */
    private fun getUUID(bleDevice: BleDevice) {
        val gatt = BleManager.get().getBluetoothGatt(bleDevice)
        gatt?.services?.forEachIndexed { index, service ->
            service.characteristics?.forEachIndexed { position, characteristics ->
                val charaProp: Int = characteristics.properties
                if (charaProp and BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) {
                    Timber.e("serviceUUID${service.uuid}")
                    Timber.e("characteristicUUID${characteristics.uuid}")
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        job?.cancel()
        BleManager.get().stopScan()
        mFingerDevice?.let {
            BleManager.get().removeBleEventCallback(it)
        }
        mBikeDevice?.let {
            BleManager.get().removeBleEventCallback(it)
        }
        mBreathDevice?.let {
            BleManager.get().removeBleEventCallback(it)
        }

    }
}