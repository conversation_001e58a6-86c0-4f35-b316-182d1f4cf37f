package com.ygxj.mcs.ui.main


import android.view.View
import androidx.core.view.isGone
import com.drake.net.Post
import com.drake.net.utils.scopeNetLife
import com.hjq.toast.Toaster
import com.ygxj.mcs.BuildConfig
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityMainBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.other.GsonConverter2
import com.ygxj.mcs.other.ActivityManager
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.ui.login.LoginActivity
import com.ygxj.mcs.ui.train.TeamRoomActivity
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.DoubleClickUtils
import com.ygxj.mcs.utils.TrainUtil
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.visible
import kotlinx.coroutines.delay


/**
 * 主页面
 */
class MainActivity : BaseActivity<ActivityMainBinding>() {

    override fun getLayoutId(): Int = R.layout.activity_main

    override fun initView() {
        setOnClickListener(
            R.id.tvName,
            R.id.llLogout,
            R.id.ibEvaluate,
            R.id.ibTrain,
            R.id.ibData,
            R.id.ibRelax,
            R.id.ibUserCenter
        )
    }

    override fun initData() {
        binding.tvName.text = UserManager.name
        if (AppConfig.enableTeam) {
            setOnlineState()
            getInvite()
        }
    }


    /**
     * 保持用户在线状态
     */
    private fun setOnlineState() {
        if (UserManager.id <= 0) return
        scopeNetLife {
            while (true) {
                Post<Any?>(Api.POST_SET_ONLINE_STATE) {
                    converter = GsonConverter2()
                    param("ID", UserManager.id)
                }.await()
                delay(1000)
            }
        }
    }

    /**
     * 请求,查看是否有邀请
     */
    private fun getInvite() {
        if (UserManager.id <= 0) return
        scopeNetLife {
            while (true) {
                val resp = Post<List<TrainListData>>(Api.POST_GET_INVITE) {
                    converter = GsonConverter2()
                    param("uid", UserManager.id)
                }.await()
                if (resp.isNotEmpty()) {
                    val invite = resp[0]
                    setUnInvite()
                    showInviteTip(invite)
                }
                delay(1000)
            }
        }
    }

    /**
     * 显示邀请提示
     */
    private fun showInviteTip(trainData: TrainListData) {
        val scene = TrainUtil.getTrainScene().find { it.name == trainData.TrainScene }
        if (scene != null) {
            DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
                ActivityManager.getInstance().getTopActivity()!!,
                "提示",
                "您当前有新的邀请训练，是否进入？"
            ) { dialog, _ ->
                dialog.dismiss()
                TeamRoomActivity.start(this@MainActivity, trainData, scene)
            }
        }
    }

    /**
     * 置为无邀请状态
     */
    private fun setUnInvite() {
        scopeNetLife {
            Post<Any?>(Api.POST_INVITE_USER) {
                converter = GsonConverter2()
                param("id", UserManager.id)
                param("yq", "0")
            }.await()
        }
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvName -> {
                if (binding.llLogout.isGone) {
                    binding.llLogout.visible()
                } else {
                    binding.llLogout.gone()
                }
            }

            R.id.llLogout -> {
                DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
                    this@MainActivity,
                    "提示",
                    "确定要退出登录吗？"
                ) { dialog, _ ->
                    dialog.dismiss()
                    UserManager.clearUserData()
                    startActivity(LoginActivity::class.java)
                    finish()
                }
            }

            R.id.ibEvaluate -> MainPageActivity.start(this, 0)

            R.id.ibTrain -> MainPageActivity.start(this, 1)

            R.id.ibData -> MainPageActivity.start(this, 2)

            R.id.ibRelax -> MainPageActivity.start(this, 3)

            R.id.ibUserCenter -> MainPageActivity.start(this, 4)
        }
    }


    override fun onBackPressed() {
        if (!DoubleClickUtils.isOnDoubleClick()) {
            Toaster.show("再按一次退出")
            return
        }
        super.onBackPressed()

    }

    override fun onDestroy() {
        super.onDestroy()

    }

}