package com.ygxj.mcs.ui.relax

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.isGone
import androidx.databinding.DataBindingUtil
import com.hjq.window.EasyWindow
import com.qmuiteam.qmui.kotlin.onClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FloatMusicBinding
import com.ygxj.mcs.utils.AudioPlayer
import com.ygxj.mcs.utils.AudioPlayer.currentIndex
import com.ygxj.mcs.utils.AudioPlayer.musicList


/**
 * 音乐播放悬浮窗
 */
class MusicView(context: Context, attributeSet: AttributeSet? = null, defStyleAttr: Int = 0) :
    FrameLayout(context, attributeSet, defStyleAttr){

    private var binding: FloatMusicBinding =
        DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.float_music, this, true)

    init {
        binding.musicName.text = musicList.getOrNull(currentIndex)?.MusicName
        AudioPlayer.isShowFloatViewContent = false
        //退出
        binding.ivClose.onClick {
            AudioPlayer.isShowFloatViewContent = false
            AudioPlayer.isShowFloatView = false
            AudioPlayer.release()
            EasyWindow.recycleAllWindow()
        }

        //设置播放按钮默认图片
        if (AudioPlayer.isPaused) {
            binding.ivPlayPause.setImageResource(R.drawable.ic_music_play_blue)
        } else {
            binding.ivPlayPause.setImageResource(R.drawable.ic_music_pause_blue)
        }

        binding.ivPlayPause.onClick {
            pauseOrPlay()
        }

        binding.ivPreview.onClick {
            playPreview()
        }

        binding.ivNext.onClick {
            playNext()
        }

        binding.rlMusic.onClick {
            if (binding.rlContent.isGone) {
                binding.rlContent.visibility = View.VISIBLE
                AudioPlayer.isShowFloatViewContent = true
            } else {
                binding.rlContent.visibility = View.GONE
                AudioPlayer.isShowFloatViewContent = false
            }
        }


        //播放完成监听
        AudioPlayer.setCompletionListener {
            playNext()
        }

        // 准备完成监听
        AudioPlayer.setPreparedListener {
            binding.ivPlayPause.setImageResource(R.drawable.ic_music_pause_blue)
        }
    }


    //点击播放按钮 暂停/播放
    private fun pauseOrPlay() {
        if (AudioPlayer.isPaused) {
            AudioPlayer.resumePlaying()
            binding.ivPlayPause.setImageResource(R.drawable.ic_music_pause_blue)
        } else {
            AudioPlayer.pausePlaying()
            binding.ivPlayPause.setImageResource(R.drawable.ic_music_play_blue)
        }
    }

    //上一首
    private fun playPreview() {
        if (musicList.isEmpty()) return

        if (currentIndex > 0) {
            currentIndex--
        } else {
            currentIndex = musicList.size - 1
        }
        AudioPlayer.stopPlaying()
        AudioPlayer.startPlaying(musicList[currentIndex].MusicUrl)
        binding.ivPlayPause.setImageResource(R.drawable.ic_music_pause_blue)
        binding.musicName.text = musicList.getOrNull(currentIndex)?.MusicName
    }

    //下一首
    private fun playNext() {
        if (musicList.isEmpty()) return

        if (currentIndex < musicList.size - 1) {
            currentIndex++
        } else {
            currentIndex = 0
        }
        AudioPlayer.stopPlaying()
        AudioPlayer.startPlaying(musicList[currentIndex].MusicUrl)
        binding.ivPlayPause.setImageResource(R.drawable.ic_music_pause_blue)
        binding.musicName.text = musicList.getOrNull(currentIndex)?.MusicName
    }


}