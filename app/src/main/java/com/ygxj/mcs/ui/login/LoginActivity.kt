package com.ygxj.mcs.ui.login


import android.view.View
import com.drake.net.Post
import com.drake.net.utils.scopeDialog
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.ygxj.mcs.BuildConfig
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityLoginBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.UserData
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.ui.dialog.DialogConfigHost
import com.ygxj.mcs.ui.dialog.DialogInputIp
import com.ygxj.mcs.ui.main.MainActivity
import timber.log.Timber


/**
 * 登录页面
 */
class LoginActivity : BaseActivity<ActivityLoginBinding>() {

    private var clickCount = 0
    override fun getLayoutId(): Int = R.layout.activity_login

    override fun initView() {
        setOnClickListener(R.id.btnLogin, R.id.tvTourist, R.id.tvRegister)

        binding.ivLogo.setOnClickListener {
            configHost()
        }
    }

    override fun initData() {
        if (BuildConfig.DEBUG) {
            binding.etName.setText("admin")
            binding.etPwd.setText("123456")
        }
    }

    /**
     * 按钮点击事件
     */
    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {

            R.id.btnLogin -> login()

            R.id.tvTourist -> guestLogin()

            R.id.tvRegister -> startActivity(RegisterActivity::class.java)
        }
    }

    /**
     * 配置Host
     */
    private fun configHost() {
        if (clickCount == 5) {
            clickCount = 0
            DialogConfigHost(this).build().show()
        } else {
            clickCount += 1
            Timber.e(clickCount.toString())
        }
    }

    /**
     * 登录
     */
    private fun login() {
        if (binding.etName.text.toString().isEmpty()) {
            Toaster.show("请输入用户名")
            return
        }
        if (binding.etPwd.text.toString().isEmpty()) {
            Toaster.show("请输入密码")
            return
        }
        scopeDialog {
            val resp = Post<UserData>(Api.POST_LOGIN) {
                param("name", binding.etName.text.toString())
                param("pwd", binding.etPwd.text.toString())
            }.await()
            UserManager.setDefaultUserData(resp)
            startActivity(MainActivity::class.java)
            finish()
        }
    }

    /**
     * 游客登录
     */
    private fun guestLogin() {
        val user = UserData()
        user.ID = -1
        user.Age = 18
        user.Invitation = -1
        user.RoleID = -1
        user.Scale = -1
        user.ThisScaleCount = -1
        user.Sex = "男"
        user.UserName = "游客账号"
        user.department = "游客"
        user.state = -1

        UserManager.setDefaultUserData(user)
        startActivity(MainActivity::class.java)
        finish()
    }


}