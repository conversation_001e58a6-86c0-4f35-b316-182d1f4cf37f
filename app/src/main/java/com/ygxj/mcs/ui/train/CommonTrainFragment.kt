package com.ygxj.mcs.ui.train


import android.os.Bundle
import android.view.View
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.channel.sendEvent
import com.drake.net.Post
import com.drake.net.utils.scope
import com.drake.net.utils.scopeNetLife
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.qmuiteam.qmui.widget.dialog.QMUIDialog
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentCommonTrainBinding
import com.ygxj.mcs.databinding.ItemTrainListBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.BaseData
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.other.GsonConverter3
import com.ygxj.mcs.other.Constants
import com.ygxj.mcs.other.ScaleManager
import com.ygxj.mcs.other.TrainType
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.evaluate.ScaleActivity
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.TrainUtil
import timber.log.Timber

/**
 * 基础训练
 * 加强训练
 */
class CommonTrainFragment : BaseFragment<TiXinTrainListActivity, FragmentCommonTrainBinding>() {

    //训练列表
    private var trainList: List<TrainListData> = listOf()

    //选中的训练任务
    private var mTrainListData: TrainListData? = null

    //训练类型
    private val mTrainType by lazy { arguments?.getInt("trainType", TrainType.NORMAL) }

    companion object {
        fun newInstance(trainType: Int): CommonTrainFragment {
            return CommonTrainFragment().apply {
                arguments = Bundle().apply {
                    putInt("trainType", trainType)
                }
            }
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_common_train
    }

    override fun initView() {
        setOnClickListener(R.id.btnTrainStart)
        initRecyclerview()
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerview() {
        binding.rv.setup {
            singleMode = true

            addType<TrainListData>(R.layout.item_train_list)

            onChecked { position, checked, _ ->
                val model = getModel<TrainListData>(position)
                model.checked = checked
                model.notifyChange()
                mTrainListData = model
            }

            onBind {
                val bind = getBinding<ItemTrainListBinding>()
                when (layoutPosition % 3) {
                    0 -> bind.item.setBackgroundResource(R.drawable.bg_train_list_item1)
                    1 -> bind.item.setBackgroundResource(R.drawable.bg_train_list_item2)
                    2 -> bind.item.setBackgroundResource(R.drawable.bg_train_list_item3)
                }
            }

            onClick(R.id.item) {
                setChecked(layoutPosition, true)
                binding.tvTrainIntro.text = TrainUtil.getTrainIntro(trainList[layoutPosition])
            }
        }
    }

    override fun initData() {
        //获取训练列表中的基础训练，后台返回的是所有训练，所以需要自己过滤
        binding.state.onRefresh {
            scope {
                val list = Post<List<TrainListData>>(Api.POST_TRAIN_LIST).await()
                when (mTrainType) {
                    TrainType.NORMAL, TrainType.STRENGTHEN -> {
                        trainList = list.filter { it.TrainType == mTrainType }
                    }
                    //身心训练,要求id匹配
                    TrainType.SHEN_XIN -> {
                        trainList =
                            list.filter { it.TrainType == mTrainType && it.TrainUsers == UserManager.id.toString() }
                    }
                    //团体训练,要求id匹配
                    TrainType.TEAM -> {
                        trainList =
                            list.filter {
                                it.TrainType == mTrainType &&
                                        it.TrainUsers.contains(UserManager.id.toString()) &&
                                        it.Display == 0 &&
                                        it.TrainName != "邀请训练"
                            }
                    }
                }
                binding.rv.models = trainList
                if (trainList.isEmpty()) {
                    showEmpty()
                } else {
                    showContent()
                    binding.rv.bindingAdapter.setChecked(0, true)
                    binding.tvTrainIntro.text = TrainUtil.getTrainIntro(trainList.first())
                }
            }
        }.showLoading()
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnTrainStart -> {
                if (mTrainType == TrainType.TEAM) {
                    startTeamTrain()
                } else {
                    scaleCheck {
                        startPersonTrain()
                    }
                }
            }
        }
    }

    /**
     * 团体训练
     */
    private fun startTeamTrain() {
        if (mTrainListData == null) return
        val sceneList = TrainUtil.getTrainScene()
        val matchingScene = sceneList.firstOrNull { it.name == mTrainListData!!.TrainScene }
        if (matchingScene != null) {
            TeamRoomActivity.start(requireContext(), mTrainListData!!, matchingScene)
        } else {
            Toaster.show("训练场景配置错误,请联系管理员修改")
        }
    }

    /**
     * 个人训练
     */
    private fun startPersonTrain() {
        if (mTrainListData == null) return
        val sceneList = TrainUtil.getTrainScene()
        val builder = QMUIDialog.MenuDialogBuilder(context)
        builder.setTitle("选择一个训练场景")
        builder.addItems(sceneList.map { it.name }.toTypedArray()) { dialog, which ->
            dialog.dismiss()
            val sceneData = sceneList[which]
            DialogUtil.showDialogWithSmartAdjustment(requireContext(), { dialog2, _ ->
                dialog2.dismiss()
                //关闭智能推荐
                TrainActivity.start(requireContext(), mTrainListData!!, sceneData,false)
            }, { dialog2, _ ->
                dialog2.dismiss()
                //开启智能推荐
                TrainActivity.start(requireContext(), mTrainListData!!, sceneData,true)
            })
        }
        builder.create().show()
    }

    /**
     * 判断是否需要填写心理评估
     */
    private fun scaleCheck(callback: () -> Unit) {
        if (!ScaleManager.id.isNullOrEmpty()) {
            callback()
            return
        }
        scopeNetLife {
            val resp = Post<BaseData<Boolean>>(Api.POST_SCALE_NECESSARY) {
                converter = GsonConverter3()
                param("id", UserManager.id)
            }.await()
            if (resp.result == true) {
                Timber.e("需填写")
                DialogUtil.showDialogWithConfirmCallBack(
                    requireContext(),
                    "提示",
                    "您此次训练前需完成心理评估，请完成后再来训练！"
                ) { dialog, _ ->
                    dialog.dismiss()
                    startActivity(ScaleActivity::class.java)
                }
            } else {
                callback()
            }
        }
    }

}