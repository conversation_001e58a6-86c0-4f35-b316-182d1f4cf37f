package com.ygxj.mcs.ui.evaluate


import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.ImageView
import androidx.lifecycle.lifecycleScope
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.net.Post
import com.drake.net.utils.scope
import com.drake.net.utils.scopeDialog
import com.hjq.toast.Toaster
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityScaleDetailBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.OptionsData
import com.ygxj.mcs.http.model.ScaleCommitData
import com.ygxj.mcs.http.model.ScaleDetailData
import com.ygxj.mcs.http.model.ScaleHistoryData
import com.ygxj.mcs.http.other.GsonConverter3
import com.ygxj.mcs.http.repository.ScaleRepository
import com.ygxj.mcs.other.ScaleManager
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.visible
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 量表详情界面
 */
class ScaleDetailActivity : BaseActivity<ActivityScaleDetailBinding>() {

    /**当前是第几题**/
    private var mTopicIndex = 0

    /**所有题目的集合**/
    private var mTopicList = arrayListOf<ScaleDetailData>()

    /**所有选项集合**/
    private var mOptions = arrayListOf<ArrayList<OptionsData>>()

    /**量表id**/
    private val mId by lazy { intent.getIntExtra("id", 0) }

    private val mTitle by lazy { intent.getStringExtra("title") }

    companion object {
        fun start(context: Context, id: Int, title: String) {
            val intent = Intent(context, ScaleDetailActivity::class.java)
            intent.putExtra("id", id)
            intent.putExtra("title", title)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_scale_detail

    override fun initView() {
        setOnClickListener(R.id.btnPre, R.id.btnNext, R.id.btnCommit)
        initRecyclerview()
        val btnBack = binding.tvTitle.findViewById<ImageView>(R.id.ivBack)
        btnBack.setOnClickListener {
            saveAnswers {
                finish()
            }
        }
    }

    override fun initData() {
        binding.tvScaleTitle.text = mTitle
        //获取本地存储的答题记录
        val scaleHistoryData = ScaleRepository.getScaleHistoryById(mId.toString())
        binding.state.onRefresh {
            scope {
                mTopicList = Post<ArrayList<ScaleDetailData>>(Api.POST_SCALE_DETAIL) {
                    param("Id", mId)
                }.await()
                binding.state.showContent()
                mTopicList.forEach {
                    val options = arrayListOf<OptionsData>()
                    if (it.Answer1 != "0" && it.Answer1.isNotBlank()) options.add(OptionsData(it.Answer1))
                    if (it.Answer2 != "0" && it.Answer2.isNotBlank()) options.add(OptionsData(it.Answer2))
                    if (it.Answer3 != "0" && it.Answer3.isNotBlank()) options.add(OptionsData(it.Answer3))
                    if (it.Answer4 != "0" && it.Answer4.isNotBlank()) options.add(OptionsData(it.Answer4))
                    if (it.Answer5 != "0" && it.Answer5.isNotBlank()) options.add(OptionsData(it.Answer5))
                    if (it.Answer6 != "0" && it.Answer6.isNotBlank()) options.add(OptionsData(it.Answer6))
                    if (it.Answer7 != "0" && it.Answer7.isNotBlank()) options.add(OptionsData(it.Answer7))
                    if (it.Answer8 != "0" && it.Answer8.isNotBlank()) options.add(OptionsData(it.Answer8))
                    if (it.Answer9 != "0" && it.Answer9.isNotBlank()) options.add(OptionsData(it.Answer9))
                    if (it.Answer10 != "0" && it.Answer10.isNotBlank()) options.add(OptionsData(it.Answer10))
                    if (it.Answer11 != "0" && it.Answer11.isNotBlank()) options.add(OptionsData(it.Answer11))
                    mOptions.add(options)
                }

                //如果本地答题记录是空，那就从第一题开始加载
                //如果不是空，那就询问用户是不是继续上次的测试
                val answers = scaleHistoryData?.answer?.split("|")
                if (answers.isNullOrEmpty()) {
                    loadQuestion()
                } else {
                    ScaleRepository.deleteScaleHistory(scaleHistoryData)
                    DialogUtil.showDialogWithConfirmAndCancelCallBack(
                        this@ScaleDetailActivity,
                        "您上次有未完成的测试进度，是否继续上次测试？", { dialog, _ ->
                            dialog.dismiss()
                            answers.forEachIndexed { index, s ->
                                mOptions[index][s.toInt() - 1].checked = true
                            }
                            if (answers.size == mTopicList.size) {
                                mTopicIndex = answers.size - 1
                                binding.btnCommit.visible()
                            } else {
                                mTopicIndex = answers.size
                            }
                            loadQuestion()
                        }, "取消", { dialog, _ ->
                            dialog.dismiss()
                            loadQuestion()
                        }
                    )
                }
            }
        }.showLoading()
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnPre -> preTopic()

            R.id.btnNext -> nextTopic()

            R.id.btnCommit -> commit()
        }
    }

    /**
     * 初始化recyclerview
     */
    private fun initRecyclerview() {
        binding.rv.setup {
            singleMode = true
            addType<OptionsData>(R.layout.item_option)

            onChecked { position, checked, allChecked ->
                val model = getModel<OptionsData>(position)
                model.checked = checked
                model.notifyChange()
            }

            onClick(R.id.item) {
                setChecked(layoutPosition, true)

                lifecycleScope.launch {
                    delay(100)
                    nextTopic()
                }
            }
        }
    }

    /**
     * 上一题
     */
    private fun preTopic() {
        if (mTopicIndex == 0) {
            Toaster.show("已经是第一题了")
            return
        }
        mTopicIndex -= 1
        loadQuestion()
    }

    /**
     * 下一题
     */
    private fun nextTopic() {
        if (mTopicIndex == mTopicList.size - 1) {
            Toaster.show("已经是最后一题了")
            binding.btnCommit.visible()
            return
        }
        val list = mOptions[mTopicIndex].filter { it.checked }
        if (list.isEmpty()) {
            Toaster.show("请选择答案")
            return
        }
        mTopicIndex += 1
        loadQuestion()
    }

    /**
     * 加载题目
     */
    private fun loadQuestion() {
        //设置题目标题
        binding.tvQuestionTitle.text = mTopicList[mTopicIndex].Topic
        //设置选项
        binding.rv.models = mOptions[mTopicIndex]
        //设置默认选中的选项
        mOptions[mTopicIndex].forEachIndexed { index, optionsData ->
            binding.rv.bindingAdapter.setChecked(index, optionsData.checked)
        }
        //设置题目序号和总题目数量
        binding.tvQuestionIndex.text = "${mTopicIndex + 1}/${mTopicList.size}"
    }

    /**
     * 提交
     */
    private fun commit() {
        val answers = arrayListOf<Int>()
        mOptions.forEach { options ->
            options.forEachIndexed { index, option ->
                if (option.checked) {
                    answers.add(index + 1)
                }
            }
        }
        scopeDialog {
            val resp = Post<ScaleCommitData?>(Api.POST_COMMIT_SCALE) {
                converter = GsonConverter3()
                param("ID", mId)
                param("Uid", UserManager.id)
                param("UserName", UserManager.name)
                param("Answer", answers.joinToString("|"))
                param("Age", UserManager.age)
                param("Sex", UserManager.sex)
                param("BirthDay", UserManager.birthday)
            }.await()
            if (resp?.code == "0") {
                ScaleManager.title = mTitle
                ScaleManager.id = mId.toString()
                DialogUtil.showAutoDismissSuccessDialogWithListener(
                    this@ScaleDetailActivity,
                    "提交成功"
                ) {
                    finish()
                }
            } else {
                Toaster.show(resp?.message)
            }

        }
    }

    /**
     * 退出的时候要判断有没有答题
     * 如果答题了需要保存下
     */
    override fun onBackPressed() {
        saveAnswers {
            super.onBackPressed()
        }
    }

    /**
     * 保存已经回答的题目
     */
    private fun saveAnswers(callback: () -> Unit) {
        val answers = arrayListOf<Int>()
        mOptions.forEach { options ->
            options.forEachIndexed { index, option ->
                if (option.checked) {
                    answers.add(index + 1)
                }
            }
        }
        if (answers.isEmpty()) {
            callback()
            return
        }

        DialogUtil.showDialogWithConfirmCallBackAndCancelButton(
            this@ScaleDetailActivity,
            "提示",
            "本次测试还未完成，确定退出吗？"
        ) { dialog, _ ->
            dialog.dismiss()
            val scaleDetailData =
                ScaleHistoryData(mId, UserManager.id, answers.joinToString("|"))
            ScaleRepository.addScaleHistory(scaleDetailData)
            DialogUtil.showAutoDismissSuccessDialogWithListener(
                this@ScaleDetailActivity,
                "测试进度保存成功"
            ) {
                callback()
            }
        }
    }
}