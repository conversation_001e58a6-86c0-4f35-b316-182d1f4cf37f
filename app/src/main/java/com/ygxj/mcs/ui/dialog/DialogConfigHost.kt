package com.ygxj.mcs.ui.dialog

import android.content.Context
import androidx.databinding.DataBindingUtil
import com.drake.net.NetConfig
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.qmuiteam.qmui.kotlin.onClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.DialogConfigHostBinding
import com.ygxj.mcs.http.api.Api
import kotlin.math.max

class DialogConfigHost(context: Context) : CenterPopupView(context) {

    private lateinit var binding: DialogConfigHostBinding

    override fun getImplLayoutId() = R.layout.dialog_config_host

    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!
        binding.etHost.setText(Api.HOST)
        //移动光标到末尾
        binding.etHost.setSelection(max(0, binding.etHost.text?.length ?: 0))

        // 保存按钮
        binding.btnSave.onClick { saveHost() }
    }

    /**
     * 保存接口地址
     */
    private fun saveHost() {
        val host = binding.etHost.text?.toString() ?: ""
        if (host.isBlank()) {
            Toaster.show("请输入主机地址")
            return
        }
        Api.HOST = host
        NetConfig.host = host
        Toaster.show("保存成功")
        dismiss()
    }

    fun build(): BasePopupView {
        return XPopup.Builder(context).dismissOnBackPressed(false).dismissOnTouchOutside(false)
            .isViewMode(true).asCustom(this)
    }
}