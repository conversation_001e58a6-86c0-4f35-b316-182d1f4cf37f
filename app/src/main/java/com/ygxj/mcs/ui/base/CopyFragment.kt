package com.ygxj.mcs.ui.base


import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentCopyBinding
import com.ygxj.mcs.ui.main.MainActivity

/**
 * 可进行拷贝的副本
 */
class CopyFragment : BaseFragment<MainActivity, FragmentCopyBinding>() {

    companion object {

        fun newInstance(): CopyFragment {
            return CopyFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_copy
    }

    override fun initView() {}

    override fun initData() {}
}