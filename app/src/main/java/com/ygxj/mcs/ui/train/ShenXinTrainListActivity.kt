package com.ygxj.mcs.ui.train


import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityShenXinTrainListBinding
import com.ygxj.mcs.other.TrainType
import com.ygxj.mcs.ui.base.BaseActivity

/**
 * 身心训练，填充[CommonTrainListFragment]
 */
class ShenXinTrainListActivity : BaseActivity<ActivityShenXinTrainListBinding>() {

    override fun getLayoutId(): Int = R.layout.activity_shen_xin_train_list

    override fun initView() {
        setOnClickListener(R.id.btnTrainStart)

        val fragment = CommonTrainFragment.newInstance(TrainType.SHEN_XIN)
        supportFragmentManager
            .beginTransaction()
            .replace(R.id.fragmentContainer, fragment)
            .commit()
    }


    override fun initData() {

    }


}