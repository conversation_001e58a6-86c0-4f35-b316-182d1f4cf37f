package com.ygxj.mcs.ui.main


import android.view.View
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentRelaxBinding
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.relax.ResourceListActivity
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.visible

/**
 * 放松体验
 */
class RelaxFragment : BaseFragment<MainPageActivity, FragmentRelaxBinding>() {

    companion object {
        fun newInstance(): RelaxFragment {
            return RelaxFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_relax
    }

    override fun initView() {
        setOnClickListener(R.id.tvMusicRelax, R.id.tvImgRelax, R.id.tvVrRelax)
        val showVR = AppConfig.enableVr
        if (showVR) {
            binding.tvVrRelax.visible()
        } else {
            binding.tvVrRelax.gone()
        }
    }

    override fun initData() {}

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvMusicRelax -> ResourceListActivity.start(requireContext(), 0)
            R.id.tvImgRelax -> ResourceListActivity.start(requireContext(), 1)
            R.id.tvVrRelax -> ResourceListActivity.start(requireContext(), 2)
        }
    }
}