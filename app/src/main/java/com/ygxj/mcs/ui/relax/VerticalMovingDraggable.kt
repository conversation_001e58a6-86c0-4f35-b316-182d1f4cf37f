package com.ygxj.mcs.ui.relax

import android.annotation.SuppressLint
import android.view.MotionEvent
import android.view.View
import com.hjq.window.draggable.BaseDraggable
import kotlin.math.max

class VerticalMovingDraggable : BaseDraggable() {
    /** 手指按下的坐标  */
    private var mViewDownX = 0f
    private var mViewDownY = 0f

    /** 触摸移动标记  */
    private var mTouchMoving = false

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouch(v: View, event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 记录按下的位置（相对 View 的坐标）
                mViewDownX = event.x
                mViewDownY = event.y
                mTouchMoving = false
            }

            MotionEvent.ACTION_MOVE -> {
                // 记录移动的位置（相对屏幕的坐标）
                val rawMoveX = event.rawX - windowInvisibleWidth
                val rawMoveY = event.rawY - windowInvisibleHeight

                var newX = rawMoveX - mViewDownX
                var newY = rawMoveY - mViewDownY

                // 判断当前是否支持移动到屏幕外
                if (!isSupportMoveOffScreen) {
                    newX = max(newX.toDouble(), 0.0).toFloat()
                    newY = max(newY.toDouble(), 0.0).toFloat()
                }

                // 更新移动的位置
                updateLocation(0f, newY)

                if (mTouchMoving) {
                    dispatchExecuteDraggingCallback()
                } else if (isFingerMove(mViewDownX, event.x, mViewDownY, event.y)) {
                    // 如果用户移动了手指，那么就拦截本次触摸事件，从而不让点击事件生效
                    mTouchMoving = true
                    dispatchStartDraggingCallback()
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (mTouchMoving) {
                    dispatchStopDraggingCallback()
                }
                try {
                    return mTouchMoving
                } finally {
                    // 重置触摸移动标记
                    mTouchMoving = false
                }
            }

            else -> {}
        }
        return mTouchMoving
    }

    /**
     * 当前是否处于触摸移动状态
     */
    override fun isTouchMoving(): Boolean {
        return mTouchMoving
    }
}