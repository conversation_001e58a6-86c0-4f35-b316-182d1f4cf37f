package com.ygxj.mcs.ui.evaluate


import android.view.View
import com.blankj.utilcode.util.SnackbarUtils.getView
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.net.Post
import com.drake.net.utils.scope
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityPressureBinding
import com.ygxj.mcs.databinding.ItemScaleListBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.ScaleListData
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.utils.loadImage
import com.ygxj.mcs.utils.visible

/**
 * 压力评估
 */
class PressureActivity : BaseActivity<ActivityPressureBinding>() {

    private var dataList: List<ScaleListData> = arrayListOf()
    override fun getLayoutId(): Int = R.layout.activity_pressure

    override fun initView() {
        val layoutManager = FlexboxLayoutManager(this).apply {
            flexDirection = FlexDirection.ROW
            flexWrap = FlexWrap.WRAP
            justifyContent = JustifyContent.CENTER
        }
        binding.rv.layoutManager = layoutManager

        binding.rv.setup {
            addType<ScaleListData>(R.layout.item_scale_list)

            onBind {
                val image = getBinding<ItemScaleListBinding>().ivScale
                when (layoutPosition % 3) {
                    0 -> image.setImageResource(R.drawable.ic_scale1)

                    1 -> image.setImageResource(R.drawable.ic_scale2)

                    2 -> image.setImageResource(R.drawable.ic_scale3)
                }
            }

            onClick(R.id.item) {
                ScaleDetailActivity.start(
                    this@PressureActivity,
                    getModel<ScaleListData>().Id,
                    getModel<ScaleListData>().MC
                )
            }
        }
    }

    override fun initData() {
        binding.state.onRefresh {
            scope {
                val resp = Post<List<ScaleListData>>(Api.POST_SCALE_LIST) {
                    param("type", 1)
                }.await()
                dataList = resp
                binding.rv.models = resp
                if (resp.isNotEmpty()) {
                    showContent()
                } else {
                    showEmpty()
                }
            }
        }.showLoading()
    }

}