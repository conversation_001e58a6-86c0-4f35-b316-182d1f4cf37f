package com.ygxj.mcs.ui.train


import android.annotation.SuppressLint
import android.graphics.Color
import android.view.View
import android.widget.SeekBar
import android.widget.TextView
import androidx.core.widget.ContentLoadingProgressBar
import androidx.lifecycle.lifecycleScope
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.brv.utils.setup
import com.drake.net.Post
import com.drake.net.utils.scope
import com.drake.net.utils.scopeDialog
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.qmuiteam.qmui.widget.dialog.QMUIDialog
import com.ygxj.mcs.BuildConfig
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentInviteTrainBinding
import com.ygxj.mcs.databinding.ItemOnlineUserBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.OnlineUsersData
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.model.TrainSceneData
import com.ygxj.mcs.http.other.GsonConverter2
import com.ygxj.mcs.other.InviteUserState
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.utils.TrainUtil
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.visible
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 邀请训练
 */
class InviteTrainFragment : BaseFragment<TiXinTrainListActivity, FragmentInviteTrainBinding>() {

    private var mTrainSceneData: TrainSceneData? = null

    private var mInviteUsersIdList = mutableListOf<String>()

    private var job: Job? = null

    companion object {
        fun newInstance(): InviteTrainFragment {
            return InviteTrainFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_invite_train
    }

    override fun initView() {
        setOnClickListener(R.id.tvTrainDuration, R.id.tvTrainScene, R.id.btnTrainStart)
        initRecyclerView()
        binding.seekBar.setOnSeekBarChangeListener(seekBarChangeListener)

    }

    override fun initData() {
        binding.state.onRefresh {
            scope {
                val resp = Post<List<OnlineUsersData>>(Api.POST_GET_ONLINE_USERS) {
                    converter = GsonConverter2()
                    param("uid", UserManager.id)
                }.await()
                if (resp.isEmpty()) {
                    showEmpty()
                } else {
                    showContent()
                    resp.forEach { e ->
                        if (mInviteUsersIdList.contains(e.ID)) {
                            e.inviteState = InviteUserState.INVITED
                        }
                    }
                    binding.rv.models = resp
                }
            }
        }.showLoading()

        job = lifecycleScope.launch {
            while (true) {
                delay(3 * 1000)
                binding.state.refresh()
            }
        }
    }

    /**
     * 初始化列表
     */
    private fun initRecyclerView() {
        binding.rv.setup {
            addType<OnlineUsersData>(R.layout.item_online_user)
            onBind {
                val model = getModel<OnlineUsersData>()
                val binding = getBinding<ItemOnlineUserBinding>()
                if (model.DisPlay == "0") {
                    setUnInviteAbleState(binding.progressBar, binding.tvInviteState, "离线")
                } else {
                    when (model.inviteState) {
                        // 已邀请,设为不可邀请状态
                        InviteUserState.INVITED -> {
                            setUnInviteAbleState(
                                binding.progressBar,
                                binding.tvInviteState,
                                "已邀请"
                            )
                        }
                        // 未邀请,设置为可邀请状态
                        InviteUserState.UNINVITED -> {
                            setInviteAbleState(binding.progressBar, binding.tvInviteState)
                        }
                        // 邀请中状态
                        InviteUserState.INVITING -> {
                            setInvitingState(binding.progressBar, binding.tvInviteState)
                        }
                    }
                }
            }

            R.id.tvInviteState.onClick {
                val model = getModel<OnlineUsersData>()
                inviteUser(model)
            }
        }
    }

    /**
     * 邀请用户
     */
    private fun inviteUser(userEntity: OnlineUsersData) {
        if (userEntity.inviteState != InviteUserState.INVITED) {
            userEntity.inviteState = InviteUserState.INVITED
            mInviteUsersIdList.add(userEntity.ID)
            binding.rv.bindingAdapter.notifyDataSetChanged()
        }
    }

    /**
     * 设置为可邀请状态
     */
    private fun setInviteAbleState(progressBar: ContentLoadingProgressBar, textView: TextView) {
        progressBar.gone()
        textView.visible()
        textView.text = "邀请"
        textView.background = textView.context.getDrawable(R.drawable.btn_invite)
    }

    /**
     * 设置为不可邀请状态
     */
    private fun setUnInviteAbleState(
        progressBar: ContentLoadingProgressBar,
        textView: TextView,
        text: String
    ) {
        progressBar.gone()
        textView.visible()
        textView.text = text
        textView.setBackgroundColor(Color.TRANSPARENT)
    }

    /**
     * 设置为邀请中状态
     */
    private fun setInvitingState(progressBar: ContentLoadingProgressBar, textView: TextView) {
        progressBar.visible()
        textView.gone()
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvTrainDuration -> showTrainDurationSelectDialog()

            R.id.tvTrainScene -> showTrainSceneSelectDialog()

            R.id.btnTrainStart -> startTrain()
        }
    }

    /**
     * seekBar滑动监听
     */
    private val seekBarChangeListener = object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            binding.tvProgress.text = "$progress%"
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {

        }

        override fun onStopTrackingTouch(seekBar: SeekBar?) {

        }
    }

    /**
     * 显示训练时长选择框
     */
    @SuppressLint("SetTextI18n")
    private fun showTrainDurationSelectDialog() {
        val builder = QMUIDialog.MenuDialogBuilder(context)
        builder.setTitle("选择训练时长")
        if (BuildConfig.DEBUG) {
            builder.addItem("3分钟") { dialog, _ ->
                dialog.dismiss()
                binding.tvTrainDuration.text = "3分钟"
            }
        }
        for (i in 3 until 12) {
            builder.addItem("${i * 5}分钟") { dialog, _ ->
                dialog.dismiss()
                binding.tvTrainDuration.text = "${i * 5}分钟"
            }
        }
        builder.create().show()
    }

    /**
     * 显示训练场景
     */
    private fun showTrainSceneSelectDialog() {
        val sceneList = TrainUtil.getTrainScene()
        val builder = QMUIDialog.MenuDialogBuilder(context)
        builder.setTitle("选择一个训练场景")
        for (scene in sceneList) {
            builder.addItem(scene.name) { dialog, which ->
                dialog.dismiss()
                binding.tvTrainScene.text = sceneList[which].name
                mTrainSceneData = sceneList[which]
            }
        }
        builder.create().show()
    }

    /**
     * 开始训练
     */
    private fun startTrain() {
        if (binding.tvTrainDuration.text.isNullOrBlank()) {
            Toaster.show("请选择训练时长")
            return
        }

        if (binding.tvTrainScene.text.isNullOrBlank()) {
            Toaster.show("请选择训练场景")
            return
        }

        if (mInviteUsersIdList.isEmpty()) {
            Toaster.show("还未邀请其他用户参与，无法创建房间")
            return
        }

        mInviteUsersIdList.add(UserManager.id.toString())
        scopeDialog {
            val resp = Post<TrainListData?>(Api.POST_CREATE_ROOM) {
                converter = GsonConverter2()
                param("name", "邀请训练")
                param("time", binding.tvTrainDuration.text.toString().replace("分钟", ""))
                param("sm", "本次训练为邀请训练")
                param("zb", "${binding.tvProgress.text.removeSuffix("%")}")
                param("zb1", "100")
                param("ts", "本次训练为邀请训练")
                param("cj", binding.tvTrainScene.text.toString())
                param("zdyh", mInviteUsersIdList.joinToString(","))
            }.await()

            mInviteUsersIdList.forEach {
                if (it != UserManager.id.toString()) {
                    Post<Any?>(Api.POST_INVITE_USER) {
                        converter = GsonConverter2()
                        param("id", it)
                        param("yq", "1")
                    }.await()
                }
            }
            TeamRoomActivity.start(requireContext(), resp!!, mTrainSceneData!!)
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        job?.cancel()
    }
}