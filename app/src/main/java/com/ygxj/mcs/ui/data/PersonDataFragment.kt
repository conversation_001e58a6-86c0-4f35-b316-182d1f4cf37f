package com.ygxj.mcs.ui.data


import com.drake.brv.utils.setup
import com.drake.net.Post
import com.drake.net.utils.scope
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentPersonDataBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.TrainResultData
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.main.MainPageActivity

/**
 * 个人数据
 */
class PersonDataFragment : BaseFragment<MainPageActivity, FragmentPersonDataBinding>() {

    companion object {
        fun newInstance(): PersonDataFragment {
            return PersonDataFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_person_data
    }

    override fun initView() {
        binding.rv.setup {
            addType<TrainResultData>(R.layout.item_person_train_data_list)

            onClick(R.id.item) {
                PersonDataDetailActivity.start(requireContext(), getModel())
            }
        }
    }

    override fun initData() {
        binding.page.onRefresh {
            scope {
                val resp = Post<List<TrainResultData>>(Api.POST_TRAIN_DATA_LIST) {
                    param("name", UserManager.name)
                }.await()
                addData(resp.filter { it.UsersCount < 2 })
            }
        }.showLoading()
    }
}