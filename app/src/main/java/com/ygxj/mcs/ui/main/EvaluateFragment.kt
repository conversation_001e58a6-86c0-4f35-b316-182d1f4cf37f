package com.ygxj.mcs.ui.main


import android.view.View
import com.drake.net.Post
import com.drake.net.utils.scope
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentEvaluateBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.ScaleListData
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.evaluate.PressureActivity
import com.ygxj.mcs.ui.evaluate.ScaleActivity
import com.ygxj.mcs.ui.evaluate.ScaleDetailActivity
import com.ygxj.mcs.utils.visible


/**
 * 心理评估
 */
class EvaluateFragment : BaseFragment<MainPageActivity, FragmentEvaluateBinding>() {

    companion object {
        fun newInstance(): EvaluateFragment {
            return EvaluateFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_evaluate
    }

    override fun initView() {
        setOnClickListener(R.id.ibScale1, R.id.ibScale2)
    }

    override fun initData() {

    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.ibScale1 -> {
                startActivity(ScaleActivity::class.java)
            }

            R.id.ibScale2 -> {
                startActivity(PressureActivity::class.java)
            }
        }
    }

}