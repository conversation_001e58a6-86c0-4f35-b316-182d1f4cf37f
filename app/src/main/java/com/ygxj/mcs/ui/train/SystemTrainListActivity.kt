package com.ygxj.mcs.ui.train


import android.content.Context
import android.content.Intent
import android.view.View
import androidx.core.content.ContentProviderCompat.requireContext
import com.drake.brv.utils.bindingAdapter
import com.drake.brv.utils.models
import com.drake.channel.sendEvent
import com.drake.net.Post
import com.drake.net.utils.scope
import com.drake.net.utils.scopeNetLife
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.qmuiteam.qmui.widget.dialog.QMUIDialog
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivitySystemTrainListBinding
import com.ygxj.mcs.databinding.ActivityTiXinTrainListBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.BaseData
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.other.GsonConverter3
import com.ygxj.mcs.other.Constants
import com.ygxj.mcs.other.FragmentPagerAdapter
import com.ygxj.mcs.other.ScaleManager
import com.ygxj.mcs.other.TrainType
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.evaluate.ScaleActivity
import com.ygxj.mcs.ui.evaluate.ScaleTrainActivity
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.TrainUtil
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.visible
import timber.log.Timber
import java.io.Serializable

/**
 * 系统推荐训练
 */
class SystemTrainListActivity : BaseActivity<ActivitySystemTrainListBinding>() {

    //是否是新手  1是  2否
    private val isNovice by lazy { intent.getIntExtra("novice", 0) }

    //是否有运动习惯  1有  2偶尔  3无
    private val isSport by lazy { intent.getIntExtra("sport", 0) }

    private var mTrainListData: TrainListData? = null

    companion object {
        fun start(context: Context, novice: Int, sport: Int) {
            val intent = Intent(context, SystemTrainListActivity::class.java)
            intent.putExtra("novice", novice)
            intent.putExtra("sport", sport)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_system_train_list

    override fun initView() {
        setOnClickListener(R.id.btnTrainStart)
        binding.tvNormalTrain.isSelected = true
        if (isNovice == 1) {
            binding.tvNormalTrain.visible()
            binding.tvStrengthTrain.gone()
        } else {
            binding.tvNormalTrain.gone()
            binding.tvStrengthTrain.visible()
        }
    }

    override fun initData() {
        //获取训练列表中的基础训练，后台返回的是所有训练，所以需要自己过滤
        binding.state.onRefresh {
            scope {
                val list = Post<List<TrainListData>>(Api.POST_TRAIN_LIST).await()
                if (isNovice == 1) {
                    when (isSport) {
                        1 -> mTrainListData = list.firstOrNull { it.TrainName == "基础训练3" }
                        2 -> mTrainListData = list.firstOrNull { it.TrainName == "基础训练2" }
                        3 -> mTrainListData = list.firstOrNull { it.TrainName == "基础训练1" }
                    }
                } else {
                    when (isSport) {
                        1 -> mTrainListData = list.firstOrNull { it.TrainName == "加强训练3" }
                        2 -> mTrainListData = list.firstOrNull { it.TrainName == "加强训练2" }
                        3 -> mTrainListData = list.firstOrNull { it.TrainName == "加强训练1" }
                    }
                }
                if (mTrainListData == null) {
                    showEmpty()
                } else {
                    showContent()
                }
                mTrainListData?.let {
                    binding.tvTrainName.text = it.TrainName
                    binding.tvTrainIntro.text = TrainUtil.getTrainIntro(it)
                }

            }
        }.showLoading()
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnTrainStart -> {
                scaleCheck()
            }
        }
    }


    /**
     * 判断是否需要填写心理评估
     */
    private fun scaleCheck() {
        if (!ScaleManager.id.isNullOrEmpty()) {
            startPersonTrain()
            return
        }
        scopeNetLife {
            val resp = Post<BaseData<Boolean>>(Api.POST_SCALE_NECESSARY) {
                converter = GsonConverter3()
                param("id", UserManager.id)
            }.await()
            if (resp.result == true) {
                Timber.e("需填写")
                DialogUtil.showDialogWithConfirmCallBack(
                    this@SystemTrainListActivity,
                    "提示",
                    "您此次训练前需完成心理评估，请完成后再来训练！"
                ) { dialog, _ ->
                    dialog.dismiss()
                    startActivity(ScaleActivity::class.java)
                }
            } else {
                startPersonTrain()
            }
        }
    }

    /**
     * 个人训练
     */
    private fun startPersonTrain() {
        if (mTrainListData == null) return
        val sceneList = TrainUtil.getTrainScene()
        val builder = QMUIDialog.MenuDialogBuilder(this)
        builder.setTitle("选择一个训练场景")
        builder.addItems(sceneList.map { it.name }.toTypedArray()) { dialog, which ->
            dialog.dismiss()
            val sceneData = sceneList[which]
            DialogUtil.showDialogWithSmartAdjustment(this@SystemTrainListActivity, { dialog2, _ ->
                dialog2.dismiss()
                //关闭智能推荐
                TrainActivity.start(this@SystemTrainListActivity, mTrainListData!!, sceneData,false)
            }, { dialog2, _ ->
                dialog2.dismiss()
                //开启智能推荐
                TrainActivity.start(this@SystemTrainListActivity, mTrainListData!!, sceneData,true)
            })
        }
        builder.create().show()
    }
}