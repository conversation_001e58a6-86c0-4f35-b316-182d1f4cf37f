// TrainViewModel.kt
package com.ygxj.mcs.ui.train

import android.content.Context
import android.os.CountDownTimer
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.scopeNetLife
import androidx.lifecycle.viewModelScope
import com.bhm.ble.BleManager
import com.drake.net.Post
import com.hjq.toast.Toaster
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.BaseData
import com.ygxj.mcs.http.model.BikeDeviceData
import com.ygxj.mcs.http.model.BreathDeviceData
import com.ygxj.mcs.http.model.FingerDeviceData
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.model.TrainSceneData
import com.ygxj.mcs.http.other.GsonConverter3
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.other.TrainType
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.evaluate.ScaleTrainActivity
import com.ygxj.mcs.utils.BikeDataUtil
import com.ygxj.mcs.utils.CustomCountDownTimer
import com.ygxj.mcs.utils.DateUtil
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.FingerDataUtil
import com.ygxj.mcs.utils.TimeUtil
import com.ygxj.mcs.utils.TimerListener
import com.ygxj.mcs.utils.getInt
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.DataOutputStream
import java.io.IOException
import java.net.InetSocketAddress
import java.net.Socket
import kotlin.math.absoluteValue

class TrainViewModel : ViewModel() {
    //训练任务
    var trainListData = TrainListData()

    //场景数据
    var trainSceneData = TrainSceneData()

    //测试者姓名
    var userName = MutableLiveData<String>()

    //场景名称
    var sceneName = MutableLiveData<String>()

    //训练时长
    var trainDuration = MutableLiveData<String>()

    private var trainDurationValue = 0

    //训练剩余时间
    private var trainRemainingTime = 0

    /**训练成功心率最大值*/
    private var trainSuccessHeartRateMax = 0.0

    /**训练成功心率最小值*/
    private var trainSuccessHeartRateMin = 0.0

    /**系统归档的累计达标时间,默认为总训练时长的三分之一*/
    private var requiredTotalSuccessTime = 0

    /**累计达标时间*/
    private var userTotalSuccessTime = 0

    //速度
    var speed = MutableLiveData("0")

    //距离
    var distance = MutableLiveData("0.00")

    //卡路里
    var calories = MutableLiveData("0")

    //训练状态
    var trainState = MutableLiveData("未开始")

    //智能调整
    var smartAdjustment = MutableLiveData(false)

    //最大心率累计时间 这个时间大于5分钟 300秒的就需要智能调整了
    var smartMaxTime = 0

    //最小心率累计时间 这个时间大于5分钟 300秒的就需要智能调整了
    var smartMinTime = 0

    /**智能调整心率最大值*/
    private var smartHeartRateMax = 0

    /**智能调整心率最小值*/
    private var smartHeartRateMin = 0

    //心率
    var heartRate = MutableLiveData("--")

    //血氧
    var bloodOxygen = MutableLiveData("--")

    //实时耗氧量
    var oxygenConsumption = MutableLiveData("--")

    //实时呼吸值
    var breathValue = MutableLiveData("--")

    //是否在训练中
    var isTraining = MutableLiveData<Boolean>()

    //vr 相关
    private var configedSocket = false
    private var socket: Socket? = null
    private var writer: DataOutputStream? = null

    /**计时器*/
    private lateinit var timer: CustomCountDownTimer

    /**
     * 记录数据标识,每10s更新为true,记录一次训练数据,然后置为false
     * 由于指脉数据频率比较快,所以用两个变量控制
     */
    private var saveBikeDataFlag = false
    private var saveFingerDataFlag = false
    private var saveBreathDataFlag = false

    /**存储心率数据*/
    private var heartRateList = arrayListOf<Int>()

    /**存储速度数据*/
    private var speedList = arrayListOf<String>()

    /**存储呼吸数据*/
    private var breathList = arrayListOf<Int>()

    /**存储耗氧量数据*/
    private var oxygenConsumptionList = arrayListOf<String>()

    //开始按钮是否可用
    var toggleBtnStartEnable = MutableLiveData<Boolean>()

    //显示倒计时5秒的组件
    var showReadyTimerText = MutableLiveData(false)
    var readyTimerText = MutableLiveData("开始")

    /**开始训练时间*/
    private var startTrainTime = ""

    /*是否跳转到设备管理界面*/
    var startDeviceManagerActivityFlag = MutableLiveData(false)

    //训练完成
    var completeTrain = MutableLiveData(false)

    /**
     * 设置默认数据
     */
    fun setDefaultData(
        context: Context,
        listData: TrainListData,
        sceneData: TrainSceneData,
        smart: Boolean
    ) {
        trainListData = listData
        trainSceneData = sceneData
        smartAdjustment.value = smart
        userName.value = UserManager.name
        sceneName.value = trainSceneData.name
//        if (BuildConfig.DEBUG) {
//            listData.TrainDuration = "1"
//        }
        trainDuration.value = "${listData.TrainDuration.getInt()}:00"

        val heartRateRange = TrainData.getTrainSuccessHeartRateRange(listData.TrainZhiBiao)
        trainSuccessHeartRateMin = heartRateRange.first//训练成功心率最小值
        trainSuccessHeartRateMax = heartRateRange.second//训练成功心率最大值

        val smartRange = TrainData.getSmartHeartRateRange()
        smartHeartRateMin = smartRange.first//智能调整心率最小值
        smartHeartRateMax = smartRange.second//智能调整心率最大值

        trainDurationValue = listData.TrainDuration.getInt()//训练时长
        requiredTotalSuccessTime =
            TrainData.getRequiredTotalSuccessTime(trainDurationValue)//系统归档的累计达标时间,默认为总训练时长的三分之一
        Timber.e("trainDuration: $trainDuration")
        Timber.e("requiredTotalSuccessTime: $requiredTotalSuccessTime")
        Timber.e("trainSuccessHeartRateMax: $trainSuccessHeartRateMax")
        Timber.e("trainSuccessHeartRateMin: $trainSuccessHeartRateMin")

        initTimer(context, trainDurationValue * 60 * 1000L)
        initSaveDataTimer()
        initUploadTimer()
    }

    /**
     * 初始化一个计时器 倒计训练时长
     */
    private fun initTimer(context: Context, totalTime: Long) {
        timer = CustomCountDownTimer(
            totalTime,
            1000,
            object : TimerListener {
                override fun onTick(millisRemain: Long) {
                    trainDuration.value = TimeUtil.formatMillionSeconds(millisRemain)
                    trainRemainingTime = (millisRemain / 1000).toInt()
                }

                override fun onFinish() {
                    //以完成状态结束训练
                    trainDuration.value = "00:00"
                    stopTrain(context)
                }
            })
    }

    //初始化一个定时任务,每隔10秒允许存储一次训练数据
    private fun initSaveDataTimer() {
        viewModelScope.launch {
            while (true) {
                delay(10 * 1000)
                saveBikeDataFlag = true
                saveFingerDataFlag = true
                saveBreathDataFlag = true
            }
        }
    }

    //初始化一个定时任务,在训练状态下,每秒向服务端和VR设备传递当前数据
    private fun initUploadTimer() {
        viewModelScope.launch {
            while (true) {
                delay(1 * 1000)
                uploadDataRealTime()
            }
        }
    }

    /**
     * 开始训练
     */
    fun startTrain(context: Context) {
        //获取已连接的设备列表
        val list = BleManager.get().getAllConnectedDevice()

        val finger = list?.any { it.deviceAddress == FingerDataUtil.fingerMac }
        val bike = list?.any { it.deviceAddress == BikeDataUtil.bikeMac }

        var tip = ""
        if (finger == false && bike == false) {
            tip = "请连接设备后再开始训练，是否立即连接？"
        } else if (finger == false) {
            tip = "请连接指脉设备后再开始训练，是否立即连接？"
        } else if (bike == false) {
            tip = "请连接单车设备后再开始训练，是否立即连接？"
        }
        if (finger == false || bike == false) {
            DialogUtil.showDialogWithConfirmCallBack(
                context,
                "提示",
                tip
            ) { dialog, _ ->
                dialog.dismiss()
                startDeviceManagerActivityFlag.value = true
            }
            return
        }

        if (trainSceneData.isVR) {
            startVrTrain(context)
            return
        }
        //团体训练和个人训练
        if (trainListData.TrainType == TrainType.TEAM) {
            startGroupTrain(context)
        } else {
            startPersonalTrain(context)
        }
    }

    /**
     * 暂停训练
     */
    fun pauseTrain(context: Context) {
        if (trainSceneData.isVR || trainListData.TrainType == TrainType.TEAM) {
            Toaster.show("该模式下无法暂停训练")
            return
        }
        if (this::timer.isInitialized) {
            timer.pause()
        }
        isTraining.value = false
    }

    /**
     * 开始vr训练
     */
    private fun startVrTrain(context: Context) {
        if (configedSocket) return
        val activity = context as TrainActivity
        activity.showLoadingDialog("正在连接VR设备")
        Thread {
            try {
                if (socket == null) socket = Socket()
                if (!socket!!.isConnected) socket?.connect(
                    InetSocketAddress(Api.vrIp, 5566), 5 * 1000
                )
                if (socket!!.isConnected) {
                    if (writer == null) {
                        writer = DataOutputStream(socket!!.getOutputStream())
                    }
                    // 开始训练
                    activity.runOnUiThread {
                        Toaster.show("VR设备已连接")
                        if (trainListData.TrainType == TrainType.TEAM) {
                            startGroupTrain(context)
                        } else {
                            startPersonalTrain(context)
                        }
                    }
                    configedSocket = true
                } else {
                    activity.runOnUiThread { connectVrFail(context) }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                activity.runOnUiThread { connectVrFail(context) }
                socket?.close()
                writer?.close()
                socket = null
                writer = null
                configedSocket = false
            } finally {
                activity.hideLoadingDialog()
            }
        }.start()
    }

    /**
     * vr设备连接失败
     */
    private fun connectVrFail(context: Context) {
        DialogUtil.showDialogWithConfirmCallBack(
            context,
            "提示",
            "无法连接到VR设备，请确保设备已打开，稍后重新尝试"
        ) { dialog, _ ->
            dialog.dismiss()
        }
    }

    /**
     * 开始个人训练
     */
    private fun startPersonalTrain(context: Context) {
        toggleBtnStartEnable.value = false
        object : CountDownTimer(6000, 1000) {
            override fun onFinish() {
                timer.start()
                showReadyTimerText.value = false
                isTraining.value = true
                startTrainTime = DateUtil.getCurrentDateTime()
                toggleBtnStartEnable.value = true
            }

            override fun onTick(millisUntilFinished: Long) {
                showReadyTimerText.value = true
                when (val remainTime = (millisUntilFinished / 1000).toInt()) {
                    0 -> readyTimerText.value = "开始"
                    6 -> readyTimerText.value = "5"
                    else -> readyTimerText.value = remainTime.toString()
                }
            }
        }.start()
    }

    /**
     * 开始团体训练
     */
    private fun startGroupTrain(context: Context) {
        timer.start()
        showReadyTimerText.value = false
        isTraining.value = true
        startTrainTime = DateUtil.getCurrentDateTime()
    }

    /**
     * 停止训练
     */
    fun stopTrain(context: Context) {
        timer.pause()
        isTraining.value = false
        commitTrainData(context)
    }

    /**
     * 保存心率数据
     */
    fun saveFingerData(context: Context, data: FingerDeviceData) {
        heartRate.value = data.heartRate.toString()
        bloodOxygen.value = data.bloodOxygen.toString()
        if (saveFingerDataFlag) {
            heartRateList.add(data.heartRate)
            if (data.heartRate.toDouble() in trainSuccessHeartRateMin..trainSuccessHeartRateMax) {
                userTotalSuccessTime += 10
                Timber.e("当前心率为:${heartRate},最小心率标准为${trainSuccessHeartRateMin},最大心率标准为${trainSuccessHeartRateMax},达标时长+10s ")
            }
            saveFingerDataFlag = false
            Timber.e("记录一次指脉数据")
        }

        //统计心率，进行智能推荐
        //首先智能推荐处于开启状态
        //其次属于训练中状态
        //最后不是团体训练
        if (smartAdjustment.value == true && isTraining.value == true && trainListData.TrainType != TrainType.TEAM) {
            if (data.heartRate > smartHeartRateMax) {
                smartMaxTime += 1
                smartMinTime = 0
                // 如果连续5分钟的心率都大于最大心率，且训练剩余时间大于15分钟，则提醒用户,并减少5分钟训练时间
                if (smartMaxTime >= 300 && trainRemainingTime > (15 * 60)) {
                    smartMaxTime = 0
                    Toaster.show("请您适当减速，不要让身体超负荷哦")
                    Timber.e("训练时间减少五分钟")
                    timer.pause()
                    initTimer(context, (trainRemainingTime - 300) * 1000L)
                    timer.start()
                }
            } else {
                smartMaxTime = 0
            }
            if (data.heartRate < smartHeartRateMin) {
                smartMaxTime = 0
                smartMinTime += 1
                // 如果连续5分钟的心率都小于最小心率，则提醒用户，并增加5分钟训练时间
                if (smartMinTime >= 300) {
                    smartMinTime = 0
                    Toaster.show("适当增加运动强度有助于更好调整身心哦")
                    Timber.e("训练时间加五分钟")
                    timer.pause()
                    initTimer(context, (trainRemainingTime + 300) * 1000L)
                    timer.start()
                }
            } else {
                smartMinTime = 0
            }
        }
    }

    /**
     * 保存单车数据
     */
    fun saveBikeData(data: BikeDeviceData) {
        speed.value = data.speedKMH.toString()
        distance.value = String.format("%.2f", data.kilometer)
        calories.value = data.calorie.toString()
        oxygenConsumption.value = data.vo2.toString()
        if (saveBikeDataFlag) {
            oxygenConsumptionList.add(String.format("%.2f", data.vo2))
            speedList.add(String.format("%.2f", data.speedKMH.absoluteValue))
            saveBikeDataFlag = false
            Timber.e("记录一次单车数据")
        }
    }

    /**
     * 保存呼吸数据
     */
    fun saveBreathData(data: BreathDeviceData) {
        breathValue.value = data.freq.toString()
        if (saveBreathDataFlag) {
            breathList.add(data.freq)
            saveBreathDataFlag = false
            Timber.e("记录一次呼吸数据")
        }
    }

    /**
     * 需要完成的功能有:
     * - 计算是否达标
     * - 请求接口提交训练数据
     * - 提交成功->提示是否查看
     * - 提交失败->提示是否重试
     * - 提交失败且不重试->提示数据无法保存,确定后再退出
     * - 请求接口记录训练次数和查看是否需要进行量表测试
     */
    private fun commitTrainData(context: Context) {
        val activity = context as TrainActivity
        activity.showLoadingDialog("训练已完成\n" + "正在保存数据")
        //构建提交服务器需要的数据
        val mileage = distance.value?.toFloat() ?: 0f
        val result = if (userTotalSuccessTime >= requiredTotalSuccessTime) "达标" else "未达标"
        val usersCount = trainListData.TrainUsers.split(",").size
        val roomId = if (usersCount > 1) trainListData.ID else 0

        val params = mapOf(
            "userName" to UserManager.name.toString(),
            "startTime" to startTrainTime,
            "endTime" to DateUtil.getCurrentDateTime(),
            "trainType" to TrainType.getTrainType(trainListData.TrainType),
            "trainScene" to trainSceneData.name,
            "mileage" to mileage.toString(),
            "avgSpeed" to String.format("%.2f", mileage * 1000 / trainDurationValue),
            "HxList" to breathList.joinToString(","),
            "calorie" to calories.value.toString(),
            "heartRateList" to heartRateList.joinToString(","),
            "speedList" to speedList.joinToString(","),
            "oxygenConsumptionList" to oxygenConsumptionList.joinToString(","),
            "result" to result,
            "usersCount" to usersCount.toString(),
            "usersName" to trainListData.TrainUsers,
            "roomId" to roomId.toString(),
        )
        //团体模式直接提交
        if (trainListData.TrainType == TrainType.TEAM) {
            commitData(context, params)
            return
        }
        //非团体模式判断是否需要进行量表测试
        scaleCheck { need ->
            if (need) {
                DialogUtil.showDialogWithConfirmCallBack(
                    context,
                    "提示",
                    "请完成心理评估",
                    { dialog, _ ->
                        ScaleTrainActivity.start(context, params)
                        activity.finish()
                    })
            } else {
                commitData(context, params)
            }
        }
    }

    /**
     * 提交
     */
    private fun commitData(context: Context, params: Map<String, String>) {
        val activity = context as TrainActivity
        scopeNetLife {
            Post<Any?>(Api.POST_COMMIT_TRAIN_RESULT) {
                params.forEach { (s, s2) ->
                    param(s, s2)
                }
            }.await()
            commitTrainCount()
            requestBeforeAndAfterTrain(false)
            activity.hideLoadingDialog()
            completeTrain.postValue(true)
        }.finally {
            activity.hideLoadingDialog()
        }
    }

    /**
     * 累加训练次数
     */
    private fun commitTrainCount() {
        scopeNetLife {
            Post<Any?>(Api.POST_COMMIT_ADD_TRAIN_COUNT) {
                converter = GsonConverter3()
                param("id", UserManager.id)
            }.await()
        }
    }

    /**
     * 按照服务端要求,开始训练前和训练结束后都要发送一次请求
     */
    fun requestBeforeAndAfterTrain(before: Boolean) {
        val url = if (before) Api.POST_REQUEST_BEFORE_TRAIN else Api.POST_REQUEST_AFTER_TRAIN
        scopeNetLife {
            Post<Any?>(url) {
                converter = GsonConverter3()
                param("uid", UserManager.id)
            }.await()
        }
    }

    /**
     * 在训练过程中,实时上传训练数据,实时向VR设备发送数据
     */
    private fun uploadDataRealTime() {
        if (isTraining.value != true) return
        // 向服务端发送数据
        var heartRate = this.heartRate.value
        var breath = this.breathValue.value
        var oxy = this.oxygenConsumption.value
        var blood = this.bloodOxygen.value

        if (heartRate == "--") heartRate = "0"
        if (breath == "--") breath = "0"
        if (oxy == "--") oxy = "0"
        if (blood == "--") blood = "0"

        scopeNetLife {
            Post<Any?>(Api.POST_UPLOAD_REAL_TIME) {
                converter = GsonConverter3()
                param("Uid", UserManager.id)
                param("TrainName", TrainType.getTrainType(trainListData.TrainType))
                param("Scene", sceneName.value)
                param("XL", heartRate)
                param("HX", breath)
                param("XY", blood)
                param("Hyl", oxy)
                param("SD", speed.value)
                param("Calorie", calories.value)
                param("Mileage", distance.value)
                param("NowTime", trainDuration.value)
                param("TrainDuration", trainDurationValue)
            }.await()
        }

        //给VR设备发送数据
        if (!configedSocket) return
        if (socket == null) return
        if (writer == null) return
        if (!socket!!.isConnected) return
        if (socket!!.isClosed) return
        // 场景名|    UID|  剩余训练时长|       速度|    距离|  卡路里|  心率值|   血氧值|  耗氧| 呼吸值
//        val msgSendToVr =
//            "${sceneName.value}|${UserManager.id}|${trainDuration.value}|${speed.value}|${distance.value}|${calories.value}|$heartRate|$blood|$oxy|$breath"

        //场景名|训练时长|速度|距离|卡路里|心率值|血氧值|耗氧|呼吸值
        val msgSendToVr =
            "${sceneName.value}|${trainDuration.value}|${speed.value}|${distance.value}|${calories.value}|$heartRate|$blood|$oxy|$breath"
        Timber.e("uploadDataRealTime: 发送给VR的数据:$msgSendToVr")
        viewModelScope.launch(Dispatchers.IO) {
            try {
                writer?.write(msgSendToVr.toByteArray())
            } catch (e: IOException) {
                // 捕获连接断开的
                e.printStackTrace()
            }
        }
    }

    /**
     * 判断是否需要填写心理评估
     */
    private fun scaleCheck(callback: (Boolean) -> Unit) {
        scopeNetLife {
            val resp = Post<BaseData<Boolean>>(Api.POST_SCALE_NECESSARY) {
                converter = GsonConverter3()
                param("id", UserManager.id)
            }.await()
            if (resp.result == true) {
                Timber.e("需填写")
                callback(true)
            } else {
                callback(false)
            }
        }
    }

}