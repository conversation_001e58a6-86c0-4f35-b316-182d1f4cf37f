package com.ygxj.mcs.ui.dialog

import android.annotation.SuppressLint
import android.content.Context
import androidx.databinding.DataBindingUtil
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.qmuiteam.qmui.kotlin.onClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.DialogInputIpBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.other.AppConfig

/**
 * 输入vr设备ip的弹框
 */
@SuppressLint("ViewConstructor")
class DialogInputIp(
    context: Context,
    private val onConfirm: (ip:String) -> Unit
) : CenterPopupView(context) {

    private lateinit var binding: DialogInputIpBinding
    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!

        binding.etIp.setText(Api.vrIp)

        binding.tvCancel.onClick {
            dismiss()
        }

        binding.tvConfirm.onClick {
            if (binding.etIp.text.isNullOrEmpty()){
                Toaster.show("IP地址不能为空")
                return@onClick
            }

            if (binding.etIp.text.toString().split(".").size != 4){
                Toaster.show("IP地址格式错误")
                return@onClick
            }

            onConfirm(binding.etIp.text.toString())
            dismiss()
        }
    }


    override fun getImplLayoutId() = R.layout.dialog_input_ip

    fun build(): BasePopupView {
        return XPopup.Builder(context).isViewMode(true).autoOpenSoftInput(true).asCustom(this)
    }
}