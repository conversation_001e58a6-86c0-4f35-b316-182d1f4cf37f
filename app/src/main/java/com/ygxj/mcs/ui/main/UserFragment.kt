package com.ygxj.mcs.ui.main


import android.view.View
import com.drake.net.Post
import com.drake.net.utils.scopeDialog
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.github.gzuliyujiang.wheelpicker.DatePicker
import com.github.gzuliyujiang.wheelpicker.entity.DateEntity
import com.hjq.toast.Toaster
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentUserBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.BaseData
import com.ygxj.mcs.http.other.GsonConverter2
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.utils.DateUtil
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.gone
import com.ygxj.mcs.utils.visible

/**
 * 个人中心
 */
class UserFragment : BaseFragment<MainPageActivity, FragmentUserBinding>() {

    private var mSex = ""

    companion object {
        fun newInstance(): UserFragment {
            return UserFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_user
    }

    override fun initView() {
        setOnClickListener(
            R.id.btnUserInfo,
            R.id.btnUserPwd,
            R.id.btnSaveUserInfo,
            R.id.btnSavePwd,
            R.id.tvBirthDate
        )
        binding.btnUserInfo.isSelected = true
        binding.rgSex.setOnCheckedChangeListener { _, i ->
            mSex = if (i == R.id.rbMale) {
                "男"
            } else {
                "女"
            }
        }
    }

    override fun initData() {
        binding.apply {
            tvUserName.text = UserManager.name
            if (UserManager.sex == "男") {
                rbMale.isChecked = true
            } else {
                rbFemale.isChecked = true
            }
            tvBirthDate.text = UserManager.birthday
            etPhone.setText(UserManager.phone)
            tvAge.text = UserManager.age
            etDepartment.setText(UserManager.department)
            etMail.setText(UserManager.email)
            etAddress.setText(UserManager.address)
        }
    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.btnUserInfo -> {
                binding.btnUserInfo.isSelected = true
                binding.btnUserPwd.isSelected = false
                binding.llUserInfo.visible()
                binding.llUserPwd.gone()
            }

            R.id.btnUserPwd -> {
                binding.btnUserInfo.isSelected = false
                binding.btnUserPwd.isSelected = true
                binding.llUserInfo.gone()
                binding.llUserPwd.visible()
            }

            R.id.tvBirthDate -> selectBirthDate()

            R.id.btnSaveUserInfo -> saveUserInfo()

            R.id.btnSavePwd -> saveUserPwd()
        }
    }

    /**
     * 选择生日
     */
    private fun selectBirthDate() {
        val picker = DatePicker(requireActivity())
        picker.wheelLayout.setResetWhenLinkage(false)
        picker.wheelLayout.setDateLabel("年", "月", "日")
        picker.wheelLayout.setRange(
            DateEntity.target(1921, 1, 1), DateEntity.today(), DateEntity.today()
        )
        picker.setOnDatePickedListener { year, month, day ->
            binding.tvBirthDate.text = DateUtil.formatDate(year, month, day)
            binding.tvAge.text =
                DateUtil.calculateAge(binding.tvBirthDate.text.toString()).toString()
        }
        picker.show()
    }

    /**
     * 保存用户信息
     */
    private fun saveUserInfo() {
        scopeDialog {
            Post<Any?>(Api.POST_UPDATE_USER_INFO) {
                param("ID", UserManager.id)
                param("Age", binding.tvAge.text.toString())
                param("Sex", mSex)
                param("birthdayTime", binding.tvBirthDate.text.toString())
                param("Telephone", binding.etMail.text.toString())
                param("Eml", binding.etMail.text.toString())
                param("address", binding.etAddress.text.toString())
                param("department", binding.etDepartment.text.toString())
            }.await()
            UserManager.age = binding.tvAge.text.toString()
            UserManager.sex = mSex
            UserManager.birthday = binding.tvBirthDate.text.toString()
            UserManager.phone = binding.etPhone.text.toString()
            UserManager.email = binding.etMail.text.toString()
            UserManager.address = binding.etAddress.text.toString()
            UserManager.department = binding.etDepartment.text.toString()
            DialogUtil.showAutoDismissSuccessDialogWithListener(requireContext(), "保存成功") {
                finish()
            }
        }
    }

    /**
     * 保存用户密码
     */
    private fun saveUserPwd() {
        if (binding.etOldPwd.text.isNullOrBlank()) {
            Toaster.show("请输入旧密码")
            return
        }
        if (binding.etNewPwd.text.isNullOrBlank()) {
            Toaster.show("请输入新密码")
            return
        }
        if (binding.etNewPwd.text.toString() != binding.etNewPwdConfirm.text.toString()) {
            Toaster.show("两次密码不一致")
            return
        }
        scopeDialog {
            Post<BaseData<Any?>>(Api.POST_UPDATE_PWD) {
                converter = GsonConverter2()
                param("Id", UserManager.id)
                param("Paw", binding.etOldPwd.text.toString())
                param("NewPaw", binding.etNewPwd.text.toString())
            }.await()
            DialogUtil.showAutoDismissSuccessDialogWithListener(requireContext(), "修改成功") {
                finish()
            }
        }
    }

}