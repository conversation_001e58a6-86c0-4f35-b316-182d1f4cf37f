package com.ygxj.mcs.ui.view

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.databinding.DataBindingUtil
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.TitleBarBinding


/**
 * 顶部的导航栏
 */
class TitleView(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {

    var binding: TitleBarBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context), R.layout.title_bar, this, true
    )

    init {
        binding.ivBack.setOnClickListener {
            val activity = context as Activity
            activity.finish()
        }
    }

    /**
     * 隐藏logo
     */
    fun hideLogo(){
        binding.ivTitleLogo.visibility = GONE
    }

}