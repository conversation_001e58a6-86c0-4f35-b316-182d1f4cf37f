package com.ygxj.mcs.ui.dialog

import android.annotation.SuppressLint
import android.content.Context
import androidx.databinding.DataBindingUtil
import com.blankj.utilcode.util.SnackbarUtils.dismiss
import com.hjq.toast.Toaster
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.CenterPopupView
import com.qmuiteam.qmui.kotlin.onClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.DialogInputIpBinding
import com.ygxj.mcs.databinding.DialogSystemTrainBinding
import com.ygxj.mcs.other.AppConfig

/**
 * 输入vr设备ip的弹框
 */
@SuppressLint("ViewConstructor")
class DialogSystemTrain(
    context: Context,
    private val onConfirm: (isNovice: Int, isSport: Int) -> Unit
) : CenterPopupView(context) {

    //是否是新手  1是  2否
    private var isNovice = 0

    //是否有运动习惯  1有  2偶尔  3无
    private var isSport = 0

    private lateinit var binding: DialogSystemTrainBinding

    override fun getImplLayoutId() = R.layout.dialog_system_train
    override fun onCreate() {
        super.onCreate()
        binding = DataBindingUtil.bind(popupImplView)!!

        binding.rgNew.setOnCheckedChangeListener { _, i ->
            when (i) {
                R.id.rb_new -> isNovice = 1

                R.id.rb_old -> isNovice = 2
            }
        }

        binding.rb1.setOnCheckedChangeListener { _, b ->
            if (b) {
                binding.rb2.isChecked = false
                binding.rb3.isChecked = false
                isSport = 1
            }
        }

        binding.rb2.setOnCheckedChangeListener { _, b ->
            if (b) {
                binding.rb1.isChecked = false
                binding.rb3.isChecked = false
                isSport = 2
            }
        }

        binding.rb3.setOnCheckedChangeListener { _, b ->
            if (b) {
                binding.rb1.isChecked = false
                binding.rb2.isChecked = false
                isSport = 3
            }
        }

        binding.tvCancel.onClick {
            dismiss()
        }

        binding.tvConfirm.onClick {
            if (isNovice == 0 || isSport == 0) {
                Toaster.show("请选择")
                return@onClick
            }
            onConfirm(isNovice, isSport)
            dismiss()
        }
    }


    fun build(): BasePopupView {
        return XPopup.Builder(context).isViewMode(true).asCustom(this)
    }
}