package com.ygxj.mcs.ui.main


import android.view.View
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentDataBinding
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.other.FragmentPagerAdapter
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.data.PersonDataFragment
import com.ygxj.mcs.ui.data.TeamDaraFragment

/**
 * 数据中心
 */
class DataFragment : BaseFragment<MainPageActivity, FragmentDataBinding>() {

    private var pagerAdapter: FragmentPagerAdapter<BaseFragment<*, *>>? = null

    companion object {
        fun newInstance(): DataFragment {
            return DataFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_data
    }

    override fun initView() {
        setOnClickListener(R.id.tvPersonal, R.id.tvTeam)
        if (AppConfig.enableTeam) {
            binding.tvTeam.visibility = View.VISIBLE
        } else {
            binding.tvTeam.visibility = View.GONE
        }
        pagerAdapter = FragmentPagerAdapter<BaseFragment<*, *>>(this).apply {
            addFragment(PersonDataFragment.newInstance())
            addFragment(TeamDaraFragment.newInstance())
            binding.vp.adapter = this
        }
        //禁止vp滑动
        binding.vp.setSwipeable(false)
    }

    override fun initData() {

    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvPersonal -> {
                binding.tvPersonal.alpha = 1f
                binding.tvTeam.alpha = 0.5f
                binding.vp.currentItem = 0
            }

            R.id.tvTeam -> {
                binding.tvPersonal.alpha = 0.5f
                binding.tvTeam.alpha = 1f
                binding.vp.currentItem = 1
            }
        }
    }
}