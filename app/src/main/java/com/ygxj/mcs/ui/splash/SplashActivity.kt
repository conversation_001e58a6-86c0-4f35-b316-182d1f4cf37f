package com.ygxj.mcs.ui.splash


import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.animation.Animation
import android.view.animation.LinearInterpolator
import android.view.animation.RotateAnimation
import androidx.lifecycle.lifecycleScope
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.ygxj.active.v2.ActivateUtilV2
import com.ygxj.mcs.BuildConfig
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivitySplashBinding
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.ui.login.LoginActivity
import com.ygxj.mcs.ui.main.MainActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


/**
 *  闪屏界面
 */
@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseActivity<ActivitySplashBinding>() {

    override fun getLayoutId(): Int = R.layout.activity_splash

    override fun initView() {
        //logo动画
        val animation = RotateAnimation(
            0f,
            360f,
            Animation.RELATIVE_TO_SELF,
            0.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f
        ).apply {
            duration = 2000
            fillAfter = true
            repeatMode = Animation.RESTART
            interpolator = LinearInterpolator()
            repeatCount = -1
        }
        binding.ivLoading.startAnimation(animation)

    }

    override fun initData() {
        if (BuildConfig.DEBUG) {
            startActivity()
        } else {
            ActivateUtilV2.getInstance(this@SplashActivity).doActivate {
                startActivity()
            }
        }
    }

    private fun startActivity() {
        lifecycleScope.launch {
            delay(1000)
            if (UserManager.id == 0) {
                startActivity(LoginActivity::class.java)
            } else {
                startActivity(MainActivity::class.java)
            }
            finish()
        }
    }

    override fun createStatusBarConfig(): ImmersionBar {
        return super.createStatusBarConfig()
            // 隐藏状态栏和导航栏
            .hideBar(BarHide.FLAG_HIDE_BAR)
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        //super.onBackPressed()
    }

    override fun initActivity() {
        // 问题及方案：https://www.cnblogs.com/net168/p/5722752.html
        // 如果当前 Activity 不是任务栈中的第一个 Activity
        if (!isTaskRoot) {
            val intent: Intent? = intent
            // 如果当前 Activity 是通过桌面图标启动进入的
            if (((intent != null) && intent.hasCategory(Intent.CATEGORY_LAUNCHER)
                        && (Intent.ACTION_MAIN == intent.action))
            ) {
                // 对当前 Activity 执行销毁操作，避免重复实例化入口
                finish()
                return
            }
        }
        super.initActivity()
    }

}