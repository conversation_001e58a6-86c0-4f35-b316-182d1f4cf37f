package com.ygxj.mcs.ui.train

import androidx.lifecycle.MutableLiveData
import com.ygxj.mcs.http.model.BikeDeviceData
import com.ygxj.mcs.http.model.BreathDeviceData
import com.ygxj.mcs.http.model.FingerDeviceData
import com.ygxj.mcs.other.AppConfig
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.utils.BikeDataUtil
import com.ygxj.mcs.utils.BreathDataUtil
import com.ygxj.mcs.utils.FingerDataUtil
import timber.log.Timber

/**
 * 处理训练中的数据，例如指脉，单车，呼吸设备的数据
 */
object TrainData {

    /**
     * 指脉是否断开
     */
    val fingerDisConnected = MutableLiveData<Boolean>()

    /**
     * 单车是否断开
     */
    val bikeDisConnected = MutableLiveData<Boolean>()

    /**
     * 呼吸是否断开
     */
    val breathDisConnected = MutableLiveData<Boolean>()

    /**
     * 指脉数据
     */
    val fingerDeviceData = MutableLiveData<FingerDeviceData?>()

    /**
     * 单车数据
     */
    val bikeDeviceData = MutableLiveData<BikeDeviceData?>()

    /**
     * 呼吸数据
     */
    val breathDeviceData = MutableLiveData<BreathDeviceData?>()

    /**
     * 解析指脉数据
     */
    fun parseFingerData(value: ByteArray) {
        val data = FingerDataUtil.parseFingerData(value)
        data?.let {
            fingerDeviceData.postValue(it)
        }
    }

    /**
     * 解析单车数据
     */
    fun parseBikeData(value: ByteArray) {
        val data = BikeDataUtil.parseBikeData(value)
        data?.let {
            bikeDeviceData.postValue(it)
        }
    }

    /**
     * 解析呼吸数据
     */
    fun parseBreathData(value: ByteArray) {
        val data = BreathDataUtil.parseBreathData(value)
        data?.let {
            breathDeviceData.postValue(it)
        }
    }

    /**
     * 重置数据
     */
    fun reset() {
        FingerDataUtil.reset()
        BikeDataUtil.reset()
        BreathDataUtil.reset()
//        fingerDisConnected.value = false
//        bikeDisConnected.value = false
//        breathDisConnected.value = false
//        fingerDeviceData.postValue(null)
//        bikeDeviceData.postValue(null)
//        breathDeviceData.postValue(null)
    }


    /**
     * 获取训练成功心率区间
     */
    fun getTrainSuccessHeartRateRange(range: String): Pair<Double, Double> {
        val userAge = UserManager.age!!.toInt()
        val percent = range.split("|")
        val percentMax: Int
        val percentMin: Int
        if (percent[0].toInt() > percent[1].toInt()) {
            percentMax = percent[0].toInt()
            percentMin = percent[1].toInt()
        } else {
            percentMax = percent[1].toInt()
            percentMin = percent[0].toInt()
        }

        Timber.e("percentMax", "percentMax:$percentMax")
        Timber.e("percentMin", "percentMin:$percentMin")

        //计算心率区间 这是新的计算公式（根据配置选择不同的计算公式 新的计算公式是临时使用的）
        if (AppConfig.ENABLE_NEW_HEART_RATE) {
            return Pair((220 - userAge) * 0.6 * 0.9 * 0.9, (220 - userAge) * 0.85 * 0.9 * 0.9)
        } else {
            //根据当前用户的年龄,计算出对应的最大正常心率值,计算公式为:心率值=206.9-(0.67*年龄)
            val normal = 206.9 - (0.67 * userAge)
            Timber.e("normal", "normal: $normal")
            return Pair(normal * percentMin / 100 * 0.9, normal * percentMax / 100)
        }
    }

    /**
     * 获取智能调整心率区间
     */
    fun getSmartHeartRateRange(): Pair<Int, Int> {
        val userAge = UserManager.age!!.toInt()
        val normal = 220 - userAge
        return Pair(normal / 2, normal)
    }

    /**
     * 获取达标时间
     */
    fun getRequiredTotalSuccessTime(trainDuration: Int): Int {
        return if (AppConfig.ENABLE_NEW_HEART_RATE) {
            (trainDuration / 3 * 2 * 0.9 * 0.9 * 60).toInt()
        } else {
            //获取系统规定的达标时间,值为训练时长的三分之一
            trainDuration / 3 * 60
        }
    }
}