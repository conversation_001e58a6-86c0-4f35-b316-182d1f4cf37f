package com.ygxj.mcs.ui.data


import com.drake.brv.utils.setup
import com.drake.net.Post
import com.drake.net.utils.scope
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentTeamDataBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.TrainResultData
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.main.MainPageActivity

/**
 * 团体数据
 */
class TeamDaraFragment : BaseFragment<MainPageActivity, FragmentTeamDataBinding>() {

    companion object {
        fun newInstance(): TeamDaraFragment {
            return TeamDaraFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_team_data
    }

    override fun initView() {
        binding.rv.setup {
            addType<TrainResultData>(R.layout.item_team_train_data_list)

            onClick(R.id.item) {
                TeamDataDetailActivity.start(requireContext(), getModel())
            }
        }
    }

    override fun initData() {
        binding.page.onRefresh {
            scope {
                val resp = Post<List<TrainResultData>>(Api.POST_TRAIN_DATA_LIST) {
                    param("name", UserManager.name)
                }.await()
                addData(resp.filter { it.UsersCount > 1 })
            }
        }.showLoading()
    }
}