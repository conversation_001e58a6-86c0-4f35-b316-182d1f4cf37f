package com.ygxj.mcs.ui.train


import android.annotation.SuppressLint
import android.view.View
import android.widget.SeekBar
import com.drake.channel.sendEvent
import com.drake.net.Post
import com.drake.net.utils.scopeNetLife
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.hjq.toast.Toaster
import com.qmuiteam.qmui.widget.dialog.QMUIDialog
import com.ygxj.mcs.BuildConfig
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.FragmentSelfTrainBinding
import com.ygxj.mcs.http.api.Api
import com.ygxj.mcs.http.model.BaseData
import com.ygxj.mcs.http.model.TrainListData
import com.ygxj.mcs.http.model.TrainSceneData
import com.ygxj.mcs.http.other.GsonConverter3
import com.ygxj.mcs.other.Constants
import com.ygxj.mcs.other.ScaleManager
import com.ygxj.mcs.other.TrainType
import com.ygxj.mcs.other.UserManager
import com.ygxj.mcs.ui.base.BaseFragment
import com.ygxj.mcs.ui.evaluate.ScaleActivity
import com.ygxj.mcs.utils.DialogUtil
import com.ygxj.mcs.utils.TrainUtil
import timber.log.Timber

/**
 * 自主训练
 */
class SelfTrainFragment : BaseFragment<TiXinTrainListActivity, FragmentSelfTrainBinding>() {

    private var mTrainSceneData: TrainSceneData? = null

    companion object {
        fun newInstance(): SelfTrainFragment {
            return SelfTrainFragment()
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_self_train
    }

    override fun initView() {
        setOnClickListener(R.id.tvTrainDuration, R.id.tvTrainScene, R.id.btnTrainStart)

        binding.seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                binding.tvProgress.text = "$progress%"
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {

            }
        })
    }

    override fun initData() {

    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvTrainDuration -> showTrainDurationSelectDialog()

            R.id.tvTrainScene -> showTrainSceneSelectDialog()

            R.id.btnTrainStart -> {
                scaleCheck {
                    startTrain()
                }
            }
        }
    }

    /**
     * 显示训练时长选择框
     */
    @SuppressLint("SetTextI18n")
    private fun showTrainDurationSelectDialog() {
        val builder = QMUIDialog.MenuDialogBuilder(context)
        builder.setTitle("选择训练时长")
        if (BuildConfig.DEBUG) {
            builder.addItem("3分钟") { dialog, _ ->
                dialog.dismiss()
                binding.tvTrainDuration.text = "3分钟"
            }
        }
        for (i in 3 until 12) {
            builder.addItem("${i * 5}分钟") { dialog, _ ->
                dialog.dismiss()
                binding.tvTrainDuration.text = "${i * 5}分钟"
            }
        }
        builder.create().show()
    }

    /**
     * 显示训练场景
     */
    private fun showTrainSceneSelectDialog() {
        val sceneList = TrainUtil.getTrainScene()
        val builder = QMUIDialog.MenuDialogBuilder(context)
        builder.setTitle("选择一个训练场景")
        for (scene in sceneList) {
            builder.addItem(scene.name) { dialog, which ->
                dialog.dismiss()
                binding.tvTrainScene.text = sceneList[which].name
                mTrainSceneData = sceneList[which]
            }
        }
        builder.create().show()
    }

    /**
     * 开始训练
     */
    private fun startTrain() {
        if (binding.tvTrainDuration.text.isNullOrBlank()) {
            Toaster.show("请选择训练时长")
            return
        }

        if (binding.tvTrainScene.text.isNullOrBlank()) {
            Toaster.show("请选择训练场景")
            return
        }
        val trainListData = TrainListData(
            Display = 0,
            ID = 0,
            TrainDuration = binding.tvTrainDuration.text.toString(),
            TrainName = "自主训练",
            TrainNotice = "本次训练为自主训练",
            TrainScene = binding.tvTrainScene.text.toString(),
            TrainTips = "本次训练为自主训练",
            TrainType = TrainType.SELF,
            TrainUsers = "",
            TrainZhiBiao = "${binding.tvProgress.text.removeSuffix("%")}|100"
        )
        DialogUtil.showDialogWithSmartAdjustment(requireContext(), { dialog2, _ ->
            dialog2.dismiss()
            //关闭智能推荐
            TrainActivity.start(requireContext(), trainListData, mTrainSceneData!!,false)
        }, { dialog2, _ ->
            dialog2.dismiss()
            //开启智能推荐
            TrainActivity.start(requireContext(), trainListData, mTrainSceneData!!,true)
        })


    }

    /**
     * 判断是否需要填写心理评估
     */
    private fun scaleCheck(callback: () -> Unit) {
        //量表id如果不为空，则不需要填写心理评估
        if (!ScaleManager.id.isNullOrEmpty()){
            callback()
            return
        }
        scopeNetLife {
            val resp = Post<BaseData<Boolean>>(Api.POST_SCALE_NECESSARY) {
                converter = GsonConverter3()
                param("id", UserManager.id)
            }.await()
            if (resp.result == true) {
                Timber.e("需填写")
                DialogUtil.showDialogWithConfirmCallBack(
                    requireContext(),
                    "提示",
                    "您此次训练前需完成心理评估，请完成后再来训练！"
                ) { dialog, _ ->
                    dialog.dismiss()
                    startActivity(ScaleActivity::class.java)
                }
            } else {
                callback()
            }
        }
    }

}