package com.ygxj.mcs.ui.train


import android.view.View
import com.flyjingfish.android_aop_core.annotations.SingleClick
import com.ygxj.mcs.R
import com.ygxj.mcs.databinding.ActivityTiXinTrainListBinding
import com.ygxj.mcs.other.FragmentPagerAdapter
import com.ygxj.mcs.other.TrainType
import com.ygxj.mcs.ui.base.BaseActivity
import com.ygxj.mcs.ui.base.BaseFragment

/**
 * 体心训练
 */
class TiXinTrainListActivity : BaseActivity<ActivityTiXinTrainListBinding>() {

    private var pagerAdapter: FragmentPagerAdapter<BaseFragment<*, *>>? = null

    override fun getLayoutId(): Int = R.layout.activity_ti_xin_train_list

    override fun initView() {
        setOnClickListener(R.id.tvSelfTrain, R.id.tvNormalTrain, R.id.tvStrengthTrain)
        binding.tvNormalTrain.isSelected = true
        pagerAdapter = FragmentPagerAdapter<BaseFragment<*, *>>(this).apply {
            addFragment(SelfTrainFragment.newInstance())
            addFragment(CommonTrainFragment.newInstance(TrainType.NORMAL))
            addFragment(CommonTrainFragment.newInstance(TrainType.STRENGTHEN))
            binding.vp.adapter = this
        }
        //禁止vp滑动
        binding.vp.setSwipeable(false)
        binding.vp.currentItem = 1
    }

    override fun initData() {

    }

    @SingleClick
    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvSelfTrain -> {
                binding.tvSelfTrain.isSelected = true
                binding.tvNormalTrain.isSelected = false
                binding.tvStrengthTrain.isSelected = false
                binding.vp.setCurrentItem(0, false)
            }

            R.id.tvNormalTrain -> {
                binding.tvSelfTrain.isSelected = false
                binding.tvNormalTrain.isSelected = true
                binding.tvStrengthTrain.isSelected = false
                binding.vp.setCurrentItem(1, false)
            }

            R.id.tvStrengthTrain -> {
                binding.tvSelfTrain.isSelected = false
                binding.tvNormalTrain.isSelected = false
                binding.tvStrengthTrain.isSelected = true
                binding.vp.setCurrentItem(2, false)
            }
        }
    }

}