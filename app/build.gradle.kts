plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("kotlin-kapt")
    id("android.aop")
}

android {
    namespace = "com.ygxj.mcs"
    compileSdk = 35
    //开启dataBinding
    buildFeatures.dataBinding = true

    defaultConfig {
        applicationId = "com.ygxj.mcs"
        minSdk = 26
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
//        create("Release") {
//            keyAlias = "key0"
//            keyPassword = "123456"
//            storeFile = file("${rootDir.absolutePath}/key.jks")
//            storePassword = "123456"
//        }
//
//        create("Debug") {
//            keyAlias = "key0"
//            keyPassword = "123456"
//            storeFile = file("${rootDir.absolutePath}/key.jks")
//            storePassword = "123456"
//        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            // 正式环境签名
            // signingConfig = signingConfigs.getByName("Release")
        }

        debug {
            // 开发环境签名
            // signingConfig = signingConfigs.getByName("Debug")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
    lint {
        disable += setOf("HardcodedText", "ContentDescription")
    }

    // 代码警告配置
}

dependencies {
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    implementation("androidx.core:core-ktx:1.15.0")
    implementation("androidx.appcompat:appcompat:1.7.0")
    implementation("com.google.android.material:material:1.12.0")
    implementation("androidx.constraintlayout:constraintlayout:2.2.1")
    // 协程
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0")
    // 要求OkHttp4以上  暂时不要用5.0以上版本
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    // 网络请求框架  https://github.com/liangjingkanji/Net
    implementation("com.github.liangjingkanji:Net:3.7.0")
    // 配合网络框架使用 吐司工具
    implementation("com.github.liangjingkanji:Tooltip:1.2.2")
    //https://github.com/liangjingkanji/Engine
    implementation("com.github.liangjingkanji:Engine:0.0.74")

    //flexbox伸缩(流式)布局
    implementation("com.google.android.flexbox:flexbox:3.0.0")

    //查看网络请求数据的框架 需要配合插件
    implementation("com.localebro:okhttpprofiler:1.0.8")

    // 列表框架 https://github.com/liangjingkanji/BRV
    implementation("com.github.liangjingkanji:BRV:1.6.0")

    // 权限请求框架：https://github.com/getActivity/XXPermissions
    implementation("com.github.getActivity:XXPermissions:20.0")

    // 标题栏框架：https://github.com/getActivity/TitleBar
    implementation("com.github.getActivity:TitleBar:10.5")

    // 吐司框架：https://github.com/getActivity/Toaster
    implementation("com.github.getActivity:Toaster:12.8")

    // Shape 框架：https://github.com/getActivity/ShapeView
    implementation("com.github.getActivity:ShapeView:9.6")

    // 图片加载框架：https://github.com/bumptech/glide
    // 官方使用文档：https://github.com/Muyangmin/glide-docs-cn
    implementation("com.github.bumptech.glide:glide:4.16.0")

    // 沉浸式框架：https://github.com/gyf-dev/ImmersionBar
    // 基础依赖包，必须要依赖
    implementation("com.geyifeng.immersionbar:immersionbar:3.2.2")
    // kotlin扩展（可选）
    implementation("com.geyifeng.immersionbar:immersionbar-ktx:3.2.2")

    // 日志打印框架：https://github.com/JakeWharton/timber
    implementation("com.jakewharton.timber:timber:5.0.1")

    // 腾讯 MMKV：https://github.com/Tencent/MMKV
    implementation("com.tencent:mmkv:2.1.0")

    // 常用工具类 https://github.com/Blankj/AndroidUtilCode
    implementation("com.blankj:utilcodex:1.31.1")

    // qmui https://github.com/Tencent/QMUI_Android
    implementation("com.qmuiteam:qmui:2.1.0")

    // 弹框 dialog https://github.com/li-xiaojun/XPopup
    implementation("com.github.li-xiaojun:XPopup:2.10.0")

    // 图表框架 https://github.com/PhilJay/MPAndroidChart
    implementation("com.github.PhilJay:MPAndroidChart:v3.1.0")

    // 视频播放 https://github.com/CarGuo/GSYVideoPlayer
    //implementation("com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer:v10.0.0")

    // PictureSelector 基础 (必须) https://github.com/LuckSiege/PictureSelector
    implementation("io.github.lucksiege:pictureselector:v3.11.2")
    // 图片压缩 (按需引入)
    implementation("io.github.lucksiege:compress:v3.11.2")
    // 图片裁剪 (按需引入)
    implementation("io.github.lucksiege:ucrop:v3.11.2")

    //悬浮窗口 https://github.com/princekin-f/EasyFloat
    //implementation("com.github.princekin-f:EasyFloat:2.0.4")
    //悬浮窗框架：https://github.com/getActivity/EasyWindow
    implementation("com.github.getActivity:EasyWindow:11.0")

    //选择器 https://github.com/gzu-liyujiang/AndroidPicker
    implementation("com.github.gzu-liyujiang.AndroidPicker:Common:4.1.14")
    implementation("com.github.gzu-liyujiang.AndroidPicker:WheelPicker:4.1.14")
    // 地址选择器
    implementation("com.github.gzu-liyujiang.AndroidPicker:AddressPicker:4.1.13")

    // Gson 解析容错：https://github.com/getActivity/GsonFactory
    implementation("com.github.getActivity:GsonFactory:9.6")
    // Gson 解析
    implementation("com.google.code.gson:gson:2.11.0")
    // Kotlin 反射库：用于反射 Kotlin data class 类对象，2.0.21 请修改成当前项目 Kotlin 的版本号
    implementation("org.jetbrains.kotlin:kotlin-reflect:2.0.21")

    //TabLayout https://github.com/angcyo/DslTabLayout
    implementation("com.github.angcyo.DslTablayout:TabLayout:3.7.0")
    implementation("com.github.angcyo.DslTablayout:ViewPager1Delegate:3.7.0")

    //轮播图 https://github.com/youth5201314/banner
    //implementation ("io.github.youth5201314:banner:2.2.3")

    // 动画解析库：https://github.com/airbnb/lottie-android
    // 动画资源：https://lottiefiles.com、https://icons8.com/animated-icons
    //implementation ("com.airbnb.android:lottie:6.6.0")

    //安卓AOP https://github.com/FlyJingFish/AndroidAOP
    implementation("io.github.FlyJingFish.AndroidAop:android-aop-core:2.2.6")
    //非必须项 👇这个包提供了一些常见的注解切面
    implementation("io.github.FlyJingFish.AndroidAop:android-aop-extra:2.2.6")

    //大屏适配 https://github.com/JessYanCoding/AndroidAutoSize
    implementation("com.github.JessYanCoding:AndroidAutoSize:v1.2.1")

    //数据库框架 https://github.com/guolindev/LitePal
    implementation("org.litepal.guolindev:core:3.2.3")

    //进度条 https://github.com/lopspower/CircularProgressBar
    implementation("com.mikhaellopez:circularprogressbar:3.1.0")

    //Android事件分发框架 https://github.com/liangjingkanji/Channel
    implementation("com.github.liangjingkanji:Channel:1.1.5")

    //exo播放器 https://developer.android.com/media/media3/exoplayer/hello-world?hl=zh-cn#kts
    implementation("androidx.media3:media3-exoplayer:1.6.1")
    implementation("androidx.media3:media3-exoplayer-dash:1.6.1")
    implementation("androidx.media3:media3-ui:1.6.1")

    //蓝牙库 https://github.com/buhuiming/BleCore
    implementation("com.github.buhuiming:BleCore:2.5.0")
    //云加密
    implementation("com.ygxj.activate-sdk:active:2.0.2")

}