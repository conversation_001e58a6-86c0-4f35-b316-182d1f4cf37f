pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven ("https://maven.aliyun.com/repository/public")
        maven ("https://maven.aliyun.com/repository/google")
        maven ("https://repo.huaweicloud.com/repository/maven")
        maven ("https://jitpack.io")
        maven {
            credentials {
                username = "6607d0bc7f227660ad200398"
                password = "SuuEmJ4Hp4L8"
            }
            url = uri("https://packages.aliyun.com/6607d0fb702faeb063ba32e0/maven/repo-activate")
        }
        google()
        mavenCentral()

    }
}

rootProject.name = "MotionCoordinationSystem"
include(":app")
 